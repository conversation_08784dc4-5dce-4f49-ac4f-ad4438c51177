'use client';

import { useState } from 'react';
import { ExternalLink, Calendar, MapPin, Award, ChevronDown, ChevronUp } from 'lucide-react';
import { ExperienceItem, getExperienceIcon, getExperienceColor } from '@/data/experience';
import { getTechnologyColor } from '@/data/projects';
import AnimatedSection from './AnimatedSection';

interface TimelineItemProps {
  experience: ExperienceItem;
  delay?: number;
  isLast?: boolean;
}

export default function TimelineItem({ experience, delay = 0, isLast = false }: TimelineItemProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const formatDate = (dateString: string) => {
    if (dateString === 'Present') return 'Present';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-NZ', { 
      year: 'numeric', 
      month: 'short' 
    });
  };

  const calculateDuration = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = endDate === 'Present' ? new Date() : new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    const diffMonths = Math.floor(diffDays / 30);
    const diffYears = Math.floor(diffMonths / 12);
    
    if (diffYears > 0) {
      const remainingMonths = diffMonths % 12;
      return `${diffYears} year${diffYears > 1 ? 's' : ''}${remainingMonths > 0 ? ` ${remainingMonths} month${remainingMonths > 1 ? 's' : ''}` : ''}`;
    } else if (diffMonths > 0) {
      return `${diffMonths} month${diffMonths > 1 ? 's' : ''}`;
    } else {
      return 'Less than a month';
    }
  };

  const getTypeLabel = (type: string) => {
    const labels: { [key: string]: string } = {
      education: 'Education',
      work: 'Work Experience',
      internship: 'Internship',
      certification: 'Certification',
      project: 'Project',
      volunteer: 'Volunteer Work'
    };
    return labels[type] || type;
  };

  return (
    <AnimatedSection delay={delay}>
      <div className="relative flex items-start space-x-4 pb-8">
        {/* Timeline Line */}
        {!isLast && (
          <div className="absolute left-6 top-12 w-0.5 h-full bg-gray-200 dark:bg-gray-700"></div>
        )}
        
        {/* Timeline Icon */}
        <div className={`flex-shrink-0 w-12 h-12 rounded-full ${getExperienceColor(experience.type)} flex items-center justify-center text-white text-xl shadow-lg z-10`}>
          <span>{getExperienceIcon(experience.type)}</span>
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
            {/* Header */}
            <div className="p-6 border-b border-gray-100 dark:border-gray-700">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  {/* Type Badge */}
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getExperienceColor(experience.type)} text-white mb-2`}>
                    {getTypeLabel(experience.type)}
                  </span>
                  
                  {/* Title and Organization */}
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-1">
                    {experience.title}
                  </h3>
                  <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-300 mb-2">
                    <span className="font-medium">{experience.organization}</span>
                    {experience.website && (
                      <a
                        href={experience.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
                      >
                        <ExternalLink className="w-4 h-4" />
                      </a>
                    )}
                  </div>
                  
                  {/* Date and Location */}
                  <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4" />
                      <span>
                        {formatDate(experience.startDate)} - {formatDate(experience.endDate)}
                      </span>
                      <span className="text-gray-400">
                        ({calculateDuration(experience.startDate, experience.endDate)})
                      </span>
                    </div>
                    <div className="flex items-center space-x-1 mt-1 sm:mt-0">
                      <MapPin className="w-4 h-4" />
                      <span>{experience.location}</span>
                    </div>
                  </div>

                  {/* Grade (for education) */}
                  {experience.grade && (
                    <div className="mt-2 flex items-center space-x-1 text-sm">
                      <Award className="w-4 h-4 text-yellow-500" />
                      <span className="font-medium text-gray-700 dark:text-gray-300">
                        {experience.grade}
                      </span>
                    </div>
                  )}
                </div>

                {/* Featured Badge */}
                {experience.featured && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                    ⭐ Featured
                  </span>
                )}
              </div>

              {/* Description */}
              <p className="text-gray-600 dark:text-gray-300 mt-4 leading-relaxed">
                {experience.description}
              </p>

              {/* Technologies */}
              {experience.technologies && experience.technologies.length > 0 && (
                <div className="mt-4">
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Technologies Used:
                  </h4>
                  <div className="flex flex-wrap gap-1">
                    {experience.technologies.slice(0, 6).map((tech) => (
                      <span
                        key={tech}
                        className={`px-2 py-1 rounded-full text-xs font-medium ${getTechnologyColor(tech)}`}
                      >
                        {tech}
                      </span>
                    ))}
                    {experience.technologies.length > 6 && (
                      <span className="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300">
                        +{experience.technologies.length - 6} more
                      </span>
                    )}
                  </div>
                </div>
              )}

              {/* Expand Button */}
              {(experience.achievements || experience.coursework || experience.skills) && (
                <button
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="mt-4 flex items-center space-x-1 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm font-medium transition-colors"
                >
                  <span>{isExpanded ? 'Show Less' : 'Show More Details'}</span>
                  {isExpanded ? (
                    <ChevronUp className="w-4 h-4" />
                  ) : (
                    <ChevronDown className="w-4 h-4" />
                  )}
                </button>
              )}
            </div>

            {/* Expanded Content */}
            {isExpanded && (
              <div className="p-6 bg-gray-50 dark:bg-gray-800 space-y-6">
                {/* Achievements */}
                {experience.achievements && experience.achievements.length > 0 && (
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                      <Award className="w-4 h-4 mr-2 text-yellow-500" />
                      Key Achievements
                    </h4>
                    <ul className="space-y-2">
                      {experience.achievements.map((achievement, index) => (
                        <li key={index} className="flex items-start space-x-2 text-sm text-gray-600 dark:text-gray-300">
                          <span className="text-green-500 mt-1">•</span>
                          <span>{achievement}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Coursework */}
                {experience.coursework && experience.coursework.length > 0 && (
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
                      Key Coursework
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {experience.coursework.map((course, index) => (
                        <div key={index} className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
                          <span className="text-blue-500">•</span>
                          <span>{course}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Skills Developed */}
                {experience.skills && experience.skills.length > 0 && (
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
                      Skills Developed
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {experience.skills.map((skill) => (
                        <span
                          key={skill}
                          className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm font-medium"
                        >
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* All Technologies (if expanded) */}
                {experience.technologies && experience.technologies.length > 6 && (
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
                      All Technologies Used
                    </h4>
                    <div className="flex flex-wrap gap-1">
                      {experience.technologies.map((tech) => (
                        <span
                          key={tech}
                          className={`px-2 py-1 rounded-full text-xs font-medium ${getTechnologyColor(tech)}`}
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </AnimatedSection>
  );
}
