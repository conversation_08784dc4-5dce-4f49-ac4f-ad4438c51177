# Deployment Guide

## 🎯 Deploying Your Portfolio to Vercel

This guide will walk you through deploying your portfolio to Vercel, making it accessible to potential employers worldwide. Vercel is free for personal projects and provides excellent performance.

## 📋 Prerequisites

Before deploying, ensure you have:

- [x] Completed portfolio development
- [x] Tested locally with `npm run dev`
- [x] Git repository set up
- [x] GitHub account
- [x] Vercel account (free)

## 🚀 Step-by-Step Deployment

### Step 1: Prepare Your Code

1. **Clean up your code:**
```bash
# Remove any console.logs or debug code
# Update placeholder content with your real information
# Test the build process
npm run build
```

2. **Update your content:**
   - Replace "Your Name" with your actual name
   - Update contact information
   - Add your real project descriptions
   - Replace placeholder images

3. **Test the production build:**
```bash
npm run build
npm run start
```

### Step 2: Set Up Git Repository

1. **Initialize Git (if not already done):**
```bash
git init
git add .
git commit -m "Initial portfolio commit"
```

2. **Create GitHub repository:**
   - Go to [GitHub.com](https://github.com)
   - Click "New repository"
   - Name it `portfolio` or `your-name-portfolio`
   - Don't initialize with README (you already have files)
   - Click "Create repository"

3. **Push to GitHub:**
```bash
git remote add origin https://github.com/yourusername/portfolio.git
git branch -M main
git push -u origin main
```

### Step 3: Deploy to Vercel

#### Option A: Vercel Dashboard (Recommended for beginners)

1. **Sign up for Vercel:**
   - Go to [vercel.com](https://vercel.com)
   - Sign up with your GitHub account
   - This automatically connects your repositories

2. **Import your project:**
   - Click "New Project"
   - Select your portfolio repository
   - Vercel will automatically detect it's a Next.js project

3. **Configure deployment:**
   - **Project Name**: `your-name-portfolio`
   - **Framework Preset**: Next.js (auto-detected)
   - **Root Directory**: `./` (default)
   - **Build Command**: `npm run build` (default)
   - **Output Directory**: `.next` (default)
   - **Install Command**: `npm install` (default)

4. **Deploy:**
   - Click "Deploy"
   - Wait 2-3 minutes for the build to complete
   - Your site will be live at `https://your-name-portfolio.vercel.app`

#### Option B: Vercel CLI (For advanced users)

1. **Install Vercel CLI:**
```bash
npm install -g vercel
```

2. **Login to Vercel:**
```bash
vercel login
```

3. **Deploy:**
```bash
vercel
```

4. **Follow the prompts:**
   - Set up and deploy? `Y`
   - Which scope? (Select your account)
   - Link to existing project? `N`
   - What's your project's name? `your-name-portfolio`
   - In which directory is your code located? `./`

### Step 4: Configure Custom Domain (Optional)

1. **Purchase a domain** (optional):
   - Recommended: Namecheap, Google Domains, or Cloudflare
   - Choose something professional like `yourname.dev` or `yourname.com`

2. **Add domain in Vercel:**
   - Go to your project dashboard
   - Click "Settings" → "Domains"
   - Add your custom domain
   - Follow DNS configuration instructions

3. **Update DNS records:**
   - Add CNAME record pointing to `cname.vercel-dns.com`
   - Or add A record pointing to Vercel's IP addresses

## 🔧 Environment Variables

If your portfolio uses any API keys or sensitive data:

1. **In Vercel Dashboard:**
   - Go to Project Settings → Environment Variables
   - Add variables like:
     - `NEXT_PUBLIC_CONTACT_EMAIL`
     - `NEXT_PUBLIC_GITHUB_USERNAME`
     - `NEXT_PUBLIC_LINKEDIN_URL`

2. **In your code:**
```typescript
const contactEmail = process.env.NEXT_PUBLIC_CONTACT_EMAIL || '<EMAIL>'
```

## 📊 Performance Optimization

### 1. Image Optimization

```typescript
// Use Next.js Image component for better performance
import Image from 'next/image'

function ProjectCard({ project }) {
  return (
    <Image
      src={project.image}
      alt={project.title}
      width={400}
      height={300}
      className="rounded-lg"
      priority={project.featured} // Load featured images first
    />
  )
}
```

### 2. SEO Optimization

Update your metadata for better search visibility:

```typescript
// src/app/layout.tsx
export const metadata: Metadata = {
  title: "John Smith - Full Stack Developer | Portfolio",
  description: "Experienced full-stack developer specializing in React, Node.js, and cloud technologies. Available for hire in New Zealand.",
  keywords: ["John Smith", "full stack developer", "React developer", "Node.js", "New Zealand", "software engineer"],
  openGraph: {
    title: "John Smith - Full Stack Developer",
    description: "Experienced full-stack developer available for hire",
    url: "https://johnsmith.dev",
    images: ["/images/og-image.jpg"],
  },
}
```

### 3. Analytics Setup

Add Google Analytics or Vercel Analytics:

```typescript
// src/app/layout.tsx
import { Analytics } from '@vercel/analytics/react'

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        {children}
        <Analytics />
      </body>
    </html>
  )
}
```

## 🔄 Continuous Deployment

Once set up, your portfolio will automatically redeploy when you push changes:

1. **Make changes locally:**
```bash
# Edit your files
git add .
git commit -m "Update project descriptions"
git push
```

2. **Automatic deployment:**
   - Vercel detects the push
   - Builds your project
   - Deploys automatically
   - Usually takes 1-2 minutes

## 🐛 Troubleshooting Common Issues

### Build Errors

**Error: "Module not found"**
```bash
# Solution: Install missing dependencies
npm install
```

**Error: "Type errors"**
```bash
# Solution: Fix TypeScript errors
npm run build
# Check the error messages and fix them
```

### Deployment Issues

**Error: "Build failed"**
1. Check build logs in Vercel dashboard
2. Test build locally: `npm run build`
3. Fix any errors and redeploy

**Error: "404 on custom domain"**
1. Check DNS configuration
2. Wait 24-48 hours for DNS propagation
3. Verify domain settings in Vercel

### Performance Issues

**Slow loading:**
1. Optimize images (use WebP format)
2. Minimize JavaScript bundles
3. Use Vercel Analytics to identify bottlenecks

## 📱 Testing Your Deployed Site

### 1. Functionality Testing
- [ ] All navigation links work
- [ ] Contact form submits (if implemented)
- [ ] External links open correctly
- [ ] Mobile responsiveness
- [ ] Dark mode toggle (if implemented)

### 2. Performance Testing
- Use [PageSpeed Insights](https://pagespeed.web.dev/)
- Aim for scores above 90
- Test on different devices and networks

### 3. SEO Testing
- Use [Google Search Console](https://search.google.com/search-console)
- Check meta tags with [Meta Tags](https://metatags.io/)
- Verify Open Graph images

## 🔒 Security Best Practices

1. **Environment Variables:**
   - Never commit API keys to Git
   - Use Vercel environment variables for sensitive data

2. **Content Security:**
   - Validate any user inputs
   - Use HTTPS (automatic with Vercel)

3. **Dependencies:**
   - Keep dependencies updated
   - Run `npm audit` regularly

## 📈 Post-Deployment Checklist

- [ ] Site loads correctly at your Vercel URL
- [ ] All sections display properly
- [ ] Contact information is correct
- [ ] Social media links work
- [ ] Resume download works
- [ ] Mobile version looks good
- [ ] Site loads quickly (< 3 seconds)
- [ ] No console errors
- [ ] Analytics tracking works
- [ ] SEO meta tags are correct

## 🎯 Sharing Your Portfolio

Once deployed, share your portfolio:

1. **Update your resume** with the portfolio URL
2. **Add to LinkedIn** in the "Contact Info" section
3. **Include in email signatures**
4. **Share on social media**
5. **Add to job applications**

## 🔄 Ongoing Maintenance

### Regular Updates
- Add new projects as you complete them
- Update skills and technologies
- Refresh project descriptions
- Update contact information

### Performance Monitoring
- Check Vercel Analytics monthly
- Monitor site speed
- Update dependencies quarterly
- Review and update content

### Content Strategy
- Blog posts (if you add a blog)
- Case studies for major projects
- Technology tutorials
- Industry insights

## 🎉 Congratulations!

Your portfolio is now live and accessible to potential employers worldwide! Remember to:

- Keep it updated with new projects
- Monitor performance and user engagement
- Continuously improve based on feedback
- Use it as a showcase of your evolving skills

Your professional portfolio URL: `https://your-name-portfolio.vercel.app`

## 📞 Getting Help

If you encounter issues:

1. **Vercel Documentation**: [vercel.com/docs](https://vercel.com/docs)
2. **Next.js Documentation**: [nextjs.org/docs](https://nextjs.org/docs)
3. **Community Support**: Vercel Discord, Stack Overflow
4. **GitHub Issues**: Check your repository's issues tab

Good luck with your job search! 🚀
