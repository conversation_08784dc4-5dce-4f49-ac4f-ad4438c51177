import Layout from '@/components/layout/Layout';
import AnimatedSection from '@/components/ui/AnimatedSection';
import SkillsSection from '@/components/sections/SkillsSection';

export default function Home() {
  return (
    <Layout>
      {/* Hero Section */}
      <section id="home" className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-indigo-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-700 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">

            {/* Left Column - Text Content */}
            <div className="text-center lg:text-left">
              <AnimatedSection delay={200}>
                <div className="mb-6">
                  <span className="inline-block px-4 py-2 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm font-medium mb-4">
                    🎓 Available for Hire
                  </span>
                </div>
              </AnimatedSection>

              <AnimatedSection delay={400}>
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6 leading-tight">
                  Hi, I'm{' '}
                  <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent animate-gradient">
                    Your Name
                  </span>
                </h1>
              </AnimatedSection>

              <AnimatedSection delay={600}>
                <h2 className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-6 font-medium">
                  Master of Applied Computing Graduate
                </h2>
              </AnimatedSection>

              <AnimatedSection delay={800}>
                <p className="text-lg text-gray-500 dark:text-gray-400 mb-8 leading-relaxed">
                  Passionate about <strong>cloud technologies</strong>, <strong>data analytics</strong>,
                  <strong> cybersecurity</strong>, and <strong>full-stack development</strong>.
                  Building innovative solutions for New Zealand's tech landscape.
                </p>
              </AnimatedSection>

              {/* Key Skills Tags */}
              <AnimatedSection delay={1000}>
                <div className="flex flex-wrap gap-2 mb-8 justify-center lg:justify-start">
                  {['AWS', 'Azure', 'React', 'Python', 'TypeScript', 'Data Analytics'].map((skill, index) => (
                    <span
                      key={skill}
                      className="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium transition-all duration-300 hover:scale-110 hover:bg-blue-100 dark:hover:bg-blue-800"
                      style={{ animationDelay: `${1200 + index * 100}ms` }}
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </AnimatedSection>

              {/* Call to Action Buttons */}
              <AnimatedSection delay={1400}>
                <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                  <a
                    href="#projects"
                    className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                  >
                    View My Projects
                  </a>
                  <a
                    href="#contact"
                    className="border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-8 py-4 rounded-lg font-medium transition-all duration-300 transform hover:scale-105"
                  >
                    Get In Touch
                  </a>
                </div>
              </AnimatedSection>
            </div>

            {/* Right Column - Professional Photo */}
            <AnimatedSection delay={600} className="flex justify-center lg:justify-end">
              <div className="relative">
                {/* Photo Container */}
                <div className="w-80 h-80 md:w-96 md:h-96 rounded-full bg-gradient-to-br from-blue-400 to-indigo-600 p-1 shadow-2xl transition-all duration-300 hover:shadow-3xl hover:scale-105">
                  <div className="w-full h-full rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center overflow-hidden">
                    {/* Placeholder for professional photo */}
                    <div className="text-center text-gray-500 dark:text-gray-400">
                      <div className="w-24 h-24 mx-auto mb-4 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                        <svg className="w-12 h-12" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                        </svg>
                      </div>
                      <p className="text-sm font-medium">Professional Photo</p>
                      <p className="text-xs opacity-75">Coming Soon</p>
                    </div>
                  </div>
                </div>

                {/* Floating Elements */}
                <div className="absolute -top-4 -right-4 w-16 h-16 bg-yellow-400 rounded-full flex items-center justify-center shadow-lg animate-float">
                  <span className="text-2xl">🚀</span>
                </div>
                <div className="absolute -bottom-4 -left-4 w-12 h-12 bg-green-400 rounded-full flex items-center justify-center shadow-lg animate-float" style={{ animationDelay: '1s' }}>
                  <span className="text-xl">💻</span>
                </div>
                <div className="absolute top-1/2 -left-8 w-8 h-8 bg-purple-400 rounded-full flex items-center justify-center shadow-lg animate-float" style={{ animationDelay: '2s' }}>
                  <span className="text-sm">⚡</span>
                </div>
              </div>
            </AnimatedSection>
          </div>

          {/* Scroll Indicator */}
          <AnimatedSection delay={2000} className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
            <div className="flex flex-col items-center text-gray-400 dark:text-gray-500">
              <p className="text-sm mb-2">Scroll to explore</p>
              <div className="w-6 h-10 border-2 border-gray-400 dark:border-gray-500 rounded-full flex justify-center">
                <div className="w-1 h-3 bg-gray-400 dark:bg-gray-500 rounded-full mt-2 animate-bounce"></div>
              </div>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Placeholder sections for now */}
      <section id="about" className="py-20 bg-white dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">About Me</h2>
          <p className="text-gray-600 dark:text-gray-300">Coming soon...</p>
        </div>
      </section>

      <SkillsSection />

      <section id="projects" className="py-20 bg-white dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">Projects</h2>
          <p className="text-gray-600 dark:text-gray-300">Coming soon...</p>
        </div>
      </section>

      <section id="experience" className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">Experience</h2>
          <p className="text-gray-600 dark:text-gray-300">Coming soon...</p>
        </div>
      </section>

      <section id="contact" className="py-20 bg-white dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">Contact</h2>
          <p className="text-gray-600 dark:text-gray-300">Coming soon...</p>
        </div>
      </section>
    </Layout>
  );
}
