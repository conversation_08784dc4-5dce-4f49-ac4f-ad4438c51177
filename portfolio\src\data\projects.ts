export interface Project {
  id: string
  title: string
  description: string
  longDescription: string
  technologies: string[]
  category: 'web' | 'mobile' | 'data' | 'cloud' | 'security' | 'automation'
  image: string
  liveUrl?: string
  githubUrl?: string
  featured: boolean
  status: 'completed' | 'in-progress' | 'planned'
  impact?: string
  challenges?: string[]
  learnings?: string[]
}

export const projects: Project[] = [
  {
    id: 'smart-farm-management',
    title: 'Smart Farm Management System',
    description: 'IoT-enabled farm management platform for New Zealand agriculture with real-time monitoring and predictive analytics.',
    longDescription: 'A comprehensive farm management system that integrates IoT sensors, weather data, and machine learning to optimize crop yields and resource usage. Built specifically for New Zealand farmers to monitor soil conditions, irrigation systems, livestock health, and weather patterns. The system provides actionable insights to improve productivity and sustainability.',
    technologies: ['React', 'TypeScript', 'Node.js', 'PostgreSQL', 'AWS IoT Core', 'Python', 'TensorFlow', 'Docker', 'AWS Lambda'],
    category: 'web',
    image: '/images/projects/farm-management.jpg',
    liveUrl: 'https://smart-farm-demo.vercel.app',
    githubUrl: 'https://github.com/yourusername/smart-farm-management',
    featured: true,
    status: 'completed',
    impact: 'Improved farm efficiency by 25% and reduced water usage by 30% in pilot testing',
    challenges: ['Integrating diverse IoT sensors', 'Handling unreliable rural internet connectivity', 'Creating intuitive UI for non-technical users'],
    learnings: ['IoT data processing at scale', 'Agricultural domain knowledge', 'Offline-first application design']
  },
  {
    id: 'nz-tourism-booking',
    title: 'NZ Tourism Experience Platform',
    description: 'Mobile-first booking platform for unique New Zealand tourism experiences with real-time availability and local guide integration.',
    longDescription: 'A React Native mobile application that connects tourists with authentic New Zealand experiences. Features include real-time booking, payment processing, location-based recommendations, weather integration, and direct communication with local guides. Supports both individual travelers and tour operators.',
    technologies: ['React Native', 'TypeScript', 'Firebase', 'Stripe API', 'Google Maps API', 'Weather API', 'Push Notifications'],
    category: 'mobile',
    image: '/images/projects/tourism-platform.jpg',
    liveUrl: 'https://nz-experiences-app.com',
    githubUrl: 'https://github.com/yourusername/nz-tourism-platform',
    featured: true,
    status: 'completed',
    impact: 'Connected 500+ tourists with local experiences, generating $50K+ in bookings',
    challenges: ['Cross-platform compatibility', 'Real-time availability synchronization', 'Payment processing compliance'],
    learnings: ['Mobile app development lifecycle', 'Payment gateway integration', 'User experience design for tourism']
  },
  {
    id: 'business-analytics-dashboard',
    title: 'SME Business Intelligence Dashboard',
    description: 'Comprehensive analytics dashboard for New Zealand small-medium enterprises with automated reporting and insights.',
    longDescription: 'A powerful business intelligence platform designed for New Zealand SMEs to track KPIs, analyze customer behavior, and generate automated reports. Integrates with popular accounting software, e-commerce platforms, and social media APIs to provide holistic business insights.',
    technologies: ['Next.js', 'TypeScript', 'Python', 'PostgreSQL', 'Redis', 'D3.js', 'Chart.js', 'Pandas', 'FastAPI'],
    category: 'data',
    image: '/images/projects/analytics-dashboard.jpg',
    liveUrl: 'https://sme-analytics-demo.vercel.app',
    githubUrl: 'https://github.com/yourusername/sme-analytics-dashboard',
    featured: true,
    status: 'completed',
    impact: 'Helped 50+ SMEs increase revenue by average of 15% through data-driven decisions',
    challenges: ['Data integration from multiple sources', 'Real-time data processing', 'Creating intuitive visualizations'],
    learnings: ['Data pipeline architecture', 'Business intelligence concepts', 'API integration patterns']
  },
  {
    id: 'cloud-infrastructure-automation',
    title: 'Cloud Infrastructure Automation Suite',
    description: 'Infrastructure as Code solution for automated AWS deployment with monitoring, scaling, and cost optimization.',
    longDescription: 'A comprehensive Infrastructure as Code solution that automates AWS resource provisioning, monitoring, and cost optimization. Includes Terraform modules, CI/CD pipelines, automated scaling policies, and comprehensive monitoring dashboards.',
    technologies: ['Terraform', 'AWS', 'Docker', 'Kubernetes', 'GitLab CI/CD', 'Prometheus', 'Grafana', 'Python', 'Bash'],
    category: 'cloud',
    image: '/images/projects/cloud-automation.jpg',
    githubUrl: 'https://github.com/yourusername/cloud-infrastructure-automation',
    featured: true,
    status: 'completed',
    impact: 'Reduced infrastructure deployment time by 80% and operational costs by 40%',
    challenges: ['Complex multi-environment deployments', 'Cost optimization strategies', 'Security compliance automation'],
    learnings: ['Infrastructure as Code best practices', 'Cloud cost optimization', 'DevOps automation patterns']
  },
  {
    id: 'cybersecurity-monitoring',
    title: 'Enterprise Security Monitoring System',
    description: 'Real-time cybersecurity monitoring and threat detection system with automated incident response capabilities.',
    longDescription: 'An enterprise-grade security monitoring system that provides real-time threat detection, automated incident response, and compliance reporting. Features machine learning-based anomaly detection, integration with SIEM tools, and automated remediation workflows.',
    technologies: ['Python', 'Elasticsearch', 'Kibana', 'Docker', 'Redis', 'PostgreSQL', 'Scikit-learn', 'FastAPI', 'Celery'],
    category: 'security',
    image: '/images/projects/security-monitoring.jpg',
    githubUrl: 'https://github.com/yourusername/security-monitoring-system',
    featured: false,
    status: 'completed',
    impact: 'Detected and prevented 95% of security threats in testing environment',
    challenges: ['Real-time log processing at scale', 'False positive reduction', 'Integration with existing security tools'],
    learnings: ['Cybersecurity best practices', 'Machine learning for anomaly detection', 'SIEM integration patterns']
  },
  {
    id: 'workflow-automation-platform',
    title: 'Business Process Automation Platform',
    description: 'No-code workflow automation platform for New Zealand businesses to streamline operations and reduce manual tasks.',
    longDescription: 'A user-friendly platform that allows businesses to create automated workflows without coding. Features drag-and-drop workflow builder, integration with popular business tools, and comprehensive analytics on process efficiency.',
    technologies: ['Vue.js', 'TypeScript', 'Node.js', 'MongoDB', 'Redis', 'Docker', 'Zapier API', 'Slack API', 'Email APIs'],
    category: 'automation',
    image: '/images/projects/automation-platform.jpg',
    liveUrl: 'https://workflow-automation-demo.com',
    githubUrl: 'https://github.com/yourusername/workflow-automation-platform',
    featured: false,
    status: 'in-progress',
    impact: 'Saved businesses an average of 10 hours per week on manual tasks',
    challenges: ['Creating intuitive no-code interface', 'Reliable workflow execution', 'Third-party API integration'],
    learnings: ['Workflow engine design', 'No-code platform development', 'Business process optimization']
  }
]

export const projectCategories = [
  { id: 'all', name: 'All Projects', icon: '🚀' },
  { id: 'web', name: 'Web Applications', icon: '🌐' },
  { id: 'mobile', name: 'Mobile Apps', icon: '📱' },
  { id: 'data', name: 'Data Analytics', icon: '📊' },
  { id: 'cloud', name: 'Cloud Solutions', icon: '☁️' },
  { id: 'security', name: 'Cybersecurity', icon: '🔒' },
  { id: 'automation', name: 'Automation', icon: '⚙️' }
]

export const getTechnologyColor = (tech: string): string => {
  const colorMap: { [key: string]: string } = {
    'React': 'bg-blue-100 text-blue-800',
    'TypeScript': 'bg-blue-100 text-blue-800',
    'Node.js': 'bg-green-100 text-green-800',
    'Python': 'bg-yellow-100 text-yellow-800',
    'AWS': 'bg-orange-100 text-orange-800',
    'Docker': 'bg-blue-100 text-blue-800',
    'PostgreSQL': 'bg-blue-100 text-blue-800',
    'MongoDB': 'bg-green-100 text-green-800',
    'Firebase': 'bg-yellow-100 text-yellow-800',
    'React Native': 'bg-blue-100 text-blue-800',
    'Vue.js': 'bg-green-100 text-green-800',
    'Next.js': 'bg-gray-100 text-gray-800',
    'Terraform': 'bg-purple-100 text-purple-800',
    'Kubernetes': 'bg-blue-100 text-blue-800',
    'TensorFlow': 'bg-orange-100 text-orange-800',
    'FastAPI': 'bg-green-100 text-green-800',
    'Elasticsearch': 'bg-yellow-100 text-yellow-800',
    'Redis': 'bg-red-100 text-red-800'
  }
  
  return colorMap[tech] || 'bg-gray-100 text-gray-800'
}
