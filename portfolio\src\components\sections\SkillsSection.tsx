'use client';

import { useState } from 'react';
import AnimatedSection from '@/components/ui/AnimatedSection';
import { Cloud, Code, Database, Shield, Smartphone, Globe } from 'lucide-react';

// Define skill categories and their data
const skillCategories = [
  {
    id: 'cloud',
    name: 'Cloud Technologies',
    icon: Cloud,
    color: 'bg-blue-500',
    skills: [
      { name: 'AWS', level: 85, description: 'EC2, S3, Lambda, RDS' },
      { name: 'Microsoft Azure', level: 80, description: 'App Service, Functions, Storage' },
      { name: 'Google Cloud', level: 70, description: 'Compute Engine, Cloud Storage' },
      { name: 'Docker', level: 75, description: 'Containerization & Orchestration' },
      { name: 'Kubernetes', level: 65, description: 'Container Orchestration' },
    ],
  },
  {
    id: 'frontend',
    name: 'Frontend Development',
    icon: Globe,
    color: 'bg-green-500',
    skills: [
      { name: 'React', level: 90, description: 'Hooks, Context, Redux' },
      { name: 'TypeScript', level: 85, description: 'Type Safety & Modern JS' },
      { name: 'Next.js', level: 80, description: 'SSR, SSG, API Routes' },
      { name: 'Tailwind CSS', level: 85, description: 'Utility-First Styling' },
      { name: 'JavaScript', level: 90, description: 'ES6+, Async/Await' },
    ],
  },
  {
    id: 'backend',
    name: 'Backend Development',
    icon: Code,
    color: 'bg-purple-500',
    skills: [
      { name: 'Node.js', level: 80, description: 'Express, APIs, Microservices' },
      { name: 'Python', level: 85, description: 'Django, Flask, FastAPI' },
      { name: 'C#/.NET', level: 75, description: 'ASP.NET Core, Web APIs' },
      { name: 'REST APIs', level: 85, description: 'RESTful Services Design' },
      { name: 'GraphQL', level: 70, description: 'Query Language & Runtime' },
    ],
  },
  {
    id: 'data',
    name: 'Data Analytics',
    icon: Database,
    color: 'bg-orange-500',
    skills: [
      { name: 'SQL', level: 85, description: 'PostgreSQL, MySQL, SQL Server' },
      { name: 'Python Data Science', level: 80, description: 'Pandas, NumPy, Matplotlib' },
      { name: 'Power BI', level: 75, description: 'Data Visualization & Reports' },
      { name: 'Tableau', level: 70, description: 'Business Intelligence' },
      { name: 'MongoDB', level: 75, description: 'NoSQL Database Design' },
    ],
  },
  {
    id: 'security',
    name: 'Cybersecurity',
    icon: Shield,
    color: 'bg-red-500',
    skills: [
      { name: 'Security Fundamentals', level: 75, description: 'OWASP, Security Principles' },
      { name: 'Authentication', level: 80, description: 'JWT, OAuth, SSO' },
      { name: 'Network Security', level: 70, description: 'Firewalls, VPN, SSL/TLS' },
      { name: 'Penetration Testing', level: 65, description: 'Vulnerability Assessment' },
      { name: 'Compliance', level: 70, description: 'ISO 27001, GDPR' },
    ],
  },
  {
    id: 'mobile',
    name: 'Mobile Development',
    icon: Smartphone,
    color: 'bg-indigo-500',
    skills: [
      { name: 'React Native', level: 75, description: 'Cross-Platform Mobile Apps' },
      { name: 'Flutter', level: 70, description: 'Dart, Cross-Platform UI' },
      { name: 'Progressive Web Apps', level: 80, description: 'PWA, Service Workers' },
      { name: 'Mobile UI/UX', level: 75, description: 'Responsive Design' },
    ],
  },
];

interface SkillBarProps {
  skill: {
    name: string;
    level: number;
    description: string;
  };
  delay: number;
}

function SkillBar({ skill, delay }: SkillBarProps) {
  return (
    <AnimatedSection delay={delay} className="mb-4">
      <div className="flex justify-between items-center mb-2">
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {skill.name}
        </span>
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {skill.level}%
        </span>
      </div>
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-1">
        <div
          className="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full transition-all duration-1000 ease-out"
          style={{
            width: `${skill.level}%`,
            transitionDelay: `${delay}ms`,
          }}
        />
      </div>
      <p className="text-xs text-gray-500 dark:text-gray-400">
        {skill.description}
      </p>
    </AnimatedSection>
  );
}

export default function SkillsSection() {
  const [activeCategory, setActiveCategory] = useState('cloud');

  const activeSkills = skillCategories.find(cat => cat.id === activeCategory)?.skills || [];

  return (
    <section id="skills" className="py-20 bg-gray-50 dark:bg-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <AnimatedSection className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Technical Skills & Expertise
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Comprehensive technical skills across cloud technologies, full-stack development, 
            data analytics, and cybersecurity - aligned with New Zealand's tech industry demands.
          </p>
        </AnimatedSection>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Category Selector */}
          <AnimatedSection delay={200} className="lg:col-span-1">
            <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                Skill Categories
              </h3>
              <div className="space-y-2">
                {skillCategories.map((category) => {
                  const IconComponent = category.icon;
                  return (
                    <button
                      key={category.id}
                      onClick={() => setActiveCategory(category.id)}
                      className={`w-full flex items-center space-x-3 p-3 rounded-lg transition-all duration-300 ${
                        activeCategory === category.id
                          ? 'bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-500'
                          : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                      }`}
                    >
                      <div className={`p-2 rounded-lg ${category.color} text-white`}>
                        <IconComponent className="w-4 h-4" />
                      </div>
                      <span className={`font-medium ${
                        activeCategory === category.id
                          ? 'text-blue-600 dark:text-blue-400'
                          : 'text-gray-700 dark:text-gray-300'
                      }`}>
                        {category.name}
                      </span>
                    </button>
                  );
                })}
              </div>
            </div>
          </AnimatedSection>

          {/* Skills Display */}
          <AnimatedSection delay={400} className="lg:col-span-2">
            <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg p-6">
              <div className="flex items-center space-x-3 mb-6">
                {(() => {
                  const activeCategory_data = skillCategories.find(cat => cat.id === activeCategory);
                  if (!activeCategory_data) return null;
                  const IconComponent = activeCategory_data.icon;
                  return (
                    <>
                      <div className={`p-3 rounded-lg ${activeCategory_data.color} text-white`}>
                        <IconComponent className="w-6 h-6" />
                      </div>
                      <h3 className="text-2xl font-semibold text-gray-900 dark:text-white">
                        {activeCategory_data.name}
                      </h3>
                    </>
                  );
                })()}
              </div>
              
              <div className="space-y-6">
                {activeSkills.map((skill, index) => (
                  <SkillBar
                    key={skill.name}
                    skill={skill}
                    delay={600 + index * 100}
                  />
                ))}
              </div>
            </div>
          </AnimatedSection>
        </div>

        {/* Quick Skills Overview */}
        <AnimatedSection delay={800} className="mt-16">
          <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg p-8">
            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6 text-center">
              Key Technologies at a Glance
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {['AWS', 'React', 'TypeScript', 'Python', 'Node.js', 'Docker', 'SQL', 'Azure', 'Next.js', 'MongoDB', 'Power BI', 'C#'].map((tech, index) => (
                <div
                  key={tech}
                  className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 text-center hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors duration-300"
                  style={{ animationDelay: `${1000 + index * 50}ms` }}
                >
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {tech}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </AnimatedSection>
      </div>
    </section>
  );
}
