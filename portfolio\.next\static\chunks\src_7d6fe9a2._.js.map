{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/BoxSync/2.%20Univeristy/5.%20Lincoln%20University/3.%20MAC/4.%20Portfolio/portfolio_webapp/portfolio/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/BoxSync/2.%20Univeristy/5.%20Lincoln%20University/3.%20MAC/4.%20Portfolio/portfolio_webapp/portfolio/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { Menu, X } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\nconst navigation = [\n  { name: 'Home', href: '#home' },\n  { name: 'About', href: '#about' },\n  { name: 'Skills', href: '#skills' },\n  { name: 'Projects', href: '#projects' },\n  { name: 'Experience', href: '#experience' },\n  { name: 'Contact', href: '#contact' },\n];\n\nexport default function Header() {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const scrollToSection = (href: string) => {\n    const element = document.querySelector(href);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n    setIsMobileMenuOpen(false);\n  };\n\n  return (\n    <header\n      className={cn(\n        'fixed top-0 left-0 right-0 z-50 transition-all duration-300',\n        isScrolled\n          ? 'bg-white/90 backdrop-blur-md shadow-lg dark:bg-gray-900/90'\n          : 'bg-transparent'\n      )}\n    >\n      <nav className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <div className=\"flex-shrink-0\">\n            <Link\n              href=\"#home\"\n              onClick={(e) => {\n                e.preventDefault();\n                scrollToSection('#home');\n              }}\n              className=\"text-2xl font-bold text-gray-900 dark:text-white\"\n            >\n              Portfolio\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-4\">\n              {navigation.map((item) => (\n                <button\n                  key={item.name}\n                  onClick={() => scrollToSection(item.href)}\n                  className=\"rounded-md px-3 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white\"\n                >\n                  {item.name}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"inline-flex items-center justify-center rounded-md p-2 text-gray-700 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white\"\n            >\n              {isMobileMenuOpen ? (\n                <X className=\"h-6 w-6\" />\n              ) : (\n                <Menu className=\"h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMobileMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"space-y-1 px-2 pb-3 pt-2 sm:px-3\">\n              {navigation.map((item) => (\n                <button\n                  key={item.name}\n                  onClick={() => scrollToSection(item.href)}\n                  className=\"block w-full rounded-md px-3 py-2 text-left text-base font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white\"\n                >\n                  {item.name}\n                </button>\n              ))}\n            </div>\n          </div>\n        )}\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;;;AALA;;;;;AAOA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAU,MAAM;IAAU;IAClC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAc,MAAM;IAAc;IAC1C;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEc,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;QACA,oBAAoB;IACtB;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA,aACI,+DACA;kBAGN,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,SAAS,CAAC;oCACR,EAAE,cAAc;oCAChB,gBAAgB;gCAClB;gCACA,WAAU;0CACX;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;wCAEC,SAAS,IAAM,gBAAgB,KAAK,IAAI;wCACxC,WAAU;kDAET,KAAK,IAAI;uCAJL,KAAK,IAAI;;;;;;;;;;;;;;;sCAWtB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,oBAAoB,CAAC;gCACpC,WAAU;0CAET,iCACC,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAEb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAOvB,kCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;gCAEC,SAAS,IAAM,gBAAgB,KAAK,IAAI;gCACxC,WAAU;0CAET,KAAK,IAAI;+BAJL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAahC;GA/FwB;KAAA", "debugId": null}}]}