'use client';

import { useState, useMemo } from 'react';
import { experienceData, experienceTypes, ExperienceItem } from '@/data/experience';
import TimelineItem from '@/components/ui/TimelineItem';
import AnimatedSection from '@/components/ui/AnimatedSection';
import { GraduationCap, Briefcase, Award, Heart, Calendar, TrendingUp } from 'lucide-react';

export default function ExperienceSection() {
  const [activeFilter, setActiveFilter] = useState('all');
  const [sortOrder, setSortOrder] = useState<'newest' | 'oldest'>('newest');

  // Filter and sort experience data
  const filteredAndSortedExperience = useMemo(() => {
    let filtered = experienceData;

    // Filter by type
    if (activeFilter !== 'all') {
      filtered = filtered.filter(item => item.type === activeFilter);
    }

    // Sort by date
    const sorted = [...filtered].sort((a, b) => {
      const dateA = new Date(a.startDate);
      const dateB = new Date(b.startDate);
      
      if (sortOrder === 'newest') {
        return dateB.getTime() - dateA.getTime();
      } else {
        return dateA.getTime() - dateB.getTime();
      }
    });

    return sorted;
  }, [activeFilter, sortOrder]);

  // Calculate statistics
  const stats = useMemo(() => {
    const education = experienceData.filter(item => item.type === 'education').length;
    const work = experienceData.filter(item => item.type === 'work' || item.type === 'internship').length;
    const certifications = experienceData.filter(item => item.type === 'certification').length;
    const volunteer = experienceData.filter(item => item.type === 'volunteer').length;

    return { education, work, certifications, volunteer };
  }, []);

  const getFilterIcon = (filterId: string) => {
    const iconMap: { [key: string]: React.ReactNode } = {
      all: <Calendar className="w-4 h-4" />,
      education: <GraduationCap className="w-4 h-4" />,
      work: <Briefcase className="w-4 h-4" />,
      internship: <TrendingUp className="w-4 h-4" />,
      certification: <Award className="w-4 h-4" />,
      volunteer: <Heart className="w-4 h-4" />
    };
    return iconMap[filterId] || <Calendar className="w-4 h-4" />;
  };

  return (
    <section id="experience" className="py-20 bg-gray-50 dark:bg-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <AnimatedSection className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Experience & Education
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
            My academic journey and professional experience in software development, 
            from foundational education to hands-on industry experience and continuous learning.
          </p>

          {/* Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-3xl mx-auto">
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
              <div className="flex items-center justify-center space-x-2 text-blue-600 dark:text-blue-400">
                <GraduationCap className="w-5 h-5" />
                <span className="text-2xl font-bold">{stats.education}</span>
              </div>
              <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">Degrees</p>
            </div>
            
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
              <div className="flex items-center justify-center space-x-2 text-green-600 dark:text-green-400">
                <Briefcase className="w-5 h-5" />
                <span className="text-2xl font-bold">{stats.work}</span>
              </div>
              <p className="text-sm text-green-700 dark:text-green-300 mt-1">Work Experience</p>
            </div>
            
            <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
              <div className="flex items-center justify-center space-x-2 text-yellow-600 dark:text-yellow-400">
                <Award className="w-5 h-5" />
                <span className="text-2xl font-bold">{stats.certifications}</span>
              </div>
              <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">Certifications</p>
            </div>
            
            <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
              <div className="flex items-center justify-center space-x-2 text-red-600 dark:text-red-400">
                <Heart className="w-5 h-5" />
                <span className="text-2xl font-bold">{stats.volunteer}</span>
              </div>
              <p className="text-sm text-red-700 dark:text-red-300 mt-1">Volunteer Work</p>
            </div>
          </div>
        </AnimatedSection>

        {/* Filters and Controls */}
        <AnimatedSection delay={200} className="mb-12">
          <div className="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-lg">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              {/* Type Filters */}
              <div className="flex flex-wrap gap-2">
                {experienceTypes.map((type) => (
                  <button
                    key={type.id}
                    onClick={() => setActiveFilter(type.id)}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                      activeFilter === type.id
                        ? 'bg-blue-600 text-white shadow-lg transform scale-105'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-600'
                    }`}
                  >
                    {getFilterIcon(type.id)}
                    <span>{type.name}</span>
                  </button>
                ))}
              </div>

              {/* Sort Controls */}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600 dark:text-gray-400">Sort by:</span>
                <select
                  value={sortOrder}
                  onChange={(e) => setSortOrder(e.target.value as 'newest' | 'oldest')}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="newest">Newest First</option>
                  <option value="oldest">Oldest First</option>
                </select>
              </div>
            </div>

            {/* Results Summary */}
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
                Showing <span className="font-semibold text-blue-600 dark:text-blue-400">{filteredAndSortedExperience.length}</span> 
                {' '}item{filteredAndSortedExperience.length !== 1 ? 's' : ''}
                {activeFilter !== 'all' && (
                  <span> in <span className="font-semibold">{experienceTypes.find(t => t.id === activeFilter)?.name}</span></span>
                )}
              </p>
            </div>
          </div>
        </AnimatedSection>

        {/* Timeline */}
        <AnimatedSection delay={400}>
          <div className="relative">
            {filteredAndSortedExperience.length > 0 ? (
              <div className="space-y-0">
                {filteredAndSortedExperience.map((experience, index) => (
                  <TimelineItem
                    key={experience.id}
                    experience={experience}
                    delay={600 + index * 100}
                    isLast={index === filteredAndSortedExperience.length - 1}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                  <Calendar className="w-12 h-12 text-gray-400" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  No experience found
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Try adjusting your filters to see more results.
                </p>
                <button
                  onClick={() => setActiveFilter('all')}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Show All Experience
                </button>
              </div>
            )}
          </div>
        </AnimatedSection>

        {/* Call to Action */}
        <AnimatedSection delay={800} className="mt-16 text-center">
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-8">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Ready to Contribute
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
              With a strong educational foundation and hands-on experience, I'm excited to bring my skills 
              to a dynamic team and continue growing as a software developer.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="#contact"
                className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                Get In Touch
              </a>
              <a
                href="/resume.pdf"
                target="_blank"
                rel="noopener noreferrer"
                className="px-8 py-3 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-600 hover:text-white transition-colors font-medium"
              >
                Download Resume
              </a>
            </div>
          </div>
        </AnimatedSection>
      </div>
    </section>
  );
}
