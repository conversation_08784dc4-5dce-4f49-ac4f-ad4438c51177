'use client';

import { useState, useMemo } from 'react';
import { projects, projectCategories, Project } from '@/data/projects';
import ProjectCard from '@/components/ui/ProjectCard';
import AnimatedSection from '@/components/ui/AnimatedSection';
import { Search, Filter, Star, Code, TrendingUp } from 'lucide-react';

export default function ProjectsSection() {
  const [activeCategory, setActiveCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showFeaturedOnly, setShowFeaturedOnly] = useState(false);

  // Filter projects based on category, search term, and featured status
  const filteredProjects = useMemo(() => {
    let filtered = projects;

    // Filter by category
    if (activeCategory !== 'all') {
      filtered = filtered.filter(project => project.category === activeCategory);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(project =>
        project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.technologies.some(tech => 
          tech.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }

    // Filter by featured status
    if (showFeaturedOnly) {
      filtered = filtered.filter(project => project.featured);
    }

    // Sort: featured first, then by status (completed first)
    return filtered.sort((a, b) => {
      if (a.featured && !b.featured) return -1;
      if (!a.featured && b.featured) return 1;
      if (a.status === 'completed' && b.status !== 'completed') return -1;
      if (a.status !== 'completed' && b.status === 'completed') return 1;
      return 0;
    });
  }, [activeCategory, searchTerm, showFeaturedOnly]);

  const featuredProjects = projects.filter(project => project.featured);
  const completedProjects = projects.filter(project => project.status === 'completed');

  return (
    <section id="projects" className="py-20 bg-white dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <AnimatedSection className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Featured Projects & Solutions
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
            Real-world applications solving problems in agriculture, tourism, business automation, 
            and cloud infrastructure - designed for the New Zealand market and beyond.
          </p>
          
          {/* Project Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-2xl mx-auto">
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
              <div className="flex items-center justify-center space-x-2 text-blue-600 dark:text-blue-400">
                <Code className="w-5 h-5" />
                <span className="text-2xl font-bold">{projects.length}</span>
              </div>
              <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">Total Projects</p>
            </div>
            
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
              <div className="flex items-center justify-center space-x-2 text-green-600 dark:text-green-400">
                <Star className="w-5 h-5" />
                <span className="text-2xl font-bold">{featuredProjects.length}</span>
              </div>
              <p className="text-sm text-green-700 dark:text-green-300 mt-1">Featured Projects</p>
            </div>
            
            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
              <div className="flex items-center justify-center space-x-2 text-purple-600 dark:text-purple-400">
                <TrendingUp className="w-5 h-5" />
                <span className="text-2xl font-bold">{completedProjects.length}</span>
              </div>
              <p className="text-sm text-purple-700 dark:text-purple-300 mt-1">Completed</p>
            </div>
          </div>
        </AnimatedSection>

        {/* Filters and Search */}
        <AnimatedSection delay={200} className="mb-12">
          <div className="bg-gray-50 dark:bg-gray-800 rounded-xl p-6">
            {/* Search Bar */}
            <div className="mb-6">
              <div className="relative max-w-md mx-auto">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search projects or technologies..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
            </div>

            {/* Category Filters */}
            <div className="flex flex-wrap justify-center gap-2 mb-4">
              {projectCategories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setActiveCategory(category.id)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                    activeCategory === category.id
                      ? 'bg-blue-600 text-white shadow-lg transform scale-105'
                      : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-600'
                  }`}
                >
                  <span>{category.icon}</span>
                  <span>{category.name}</span>
                </button>
              ))}
            </div>

            {/* Additional Filters */}
            <div className="flex justify-center">
              <button
                onClick={() => setShowFeaturedOnly(!showFeaturedOnly)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                  showFeaturedOnly
                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                    : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-yellow-50 dark:hover:bg-gray-600'
                }`}
              >
                <Star className="w-4 h-4" />
                <span>Featured Only</span>
              </button>
            </div>
          </div>
        </AnimatedSection>

        {/* Results Summary */}
        <AnimatedSection delay={300} className="mb-8">
          <div className="text-center">
            <p className="text-gray-600 dark:text-gray-300">
              Showing <span className="font-semibold text-blue-600 dark:text-blue-400">{filteredProjects.length}</span> 
              {' '}project{filteredProjects.length !== 1 ? 's' : ''}
              {activeCategory !== 'all' && (
                <span> in <span className="font-semibold">{projectCategories.find(cat => cat.id === activeCategory)?.name}</span></span>
              )}
              {searchTerm && (
                <span> matching "<span className="font-semibold">{searchTerm}</span>"</span>
              )}
            </p>
          </div>
        </AnimatedSection>

        {/* Projects Grid */}
        {filteredProjects.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredProjects.map((project, index) => (
              <ProjectCard
                key={project.id}
                project={project}
                delay={400 + index * 100}
              />
            ))}
          </div>
        ) : (
          <AnimatedSection delay={400}>
            <div className="text-center py-12">
              <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                <Filter className="w-12 h-12 text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                No projects found
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                Try adjusting your search terms or filters to find what you're looking for.
              </p>
              <button
                onClick={() => {
                  setSearchTerm('');
                  setActiveCategory('all');
                  setShowFeaturedOnly(false);
                }}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Clear Filters
              </button>
            </div>
          </AnimatedSection>
        )}

        {/* Call to Action */}
        <AnimatedSection delay={600} className="mt-16 text-center">
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-8">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Interested in Working Together?
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
              I'm always excited to work on new projects and solve challenging problems. 
              Let's discuss how we can bring your ideas to life.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="#contact"
                className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                Get In Touch
              </a>
              <a
                href="https://github.com/yourusername"
                target="_blank"
                rel="noopener noreferrer"
                className="px-8 py-3 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-600 hover:text-white transition-colors font-medium"
              >
                View All Code
              </a>
            </div>
          </div>
        </AnimatedSection>
      </div>
    </section>
  );
}
