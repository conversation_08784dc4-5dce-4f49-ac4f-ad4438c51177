{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/BoxSync/2.%20Univeristy/5.%20Lincoln%20University/3.%20MAC/4.%20Portfolio/portfolio_webapp/portfolio/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/BoxSync/2.%20Univeristy/5.%20Lincoln%20University/3.%20MAC/4.%20Portfolio/portfolio_webapp/portfolio/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { Menu, X } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\nconst navigation = [\n  { name: 'Home', href: '#home' },\n  { name: 'About', href: '#about' },\n  { name: 'Skills', href: '#skills' },\n  { name: 'Projects', href: '#projects' },\n  { name: 'Experience', href: '#experience' },\n  { name: 'Contact', href: '#contact' },\n];\n\nexport default function Header() {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const scrollToSection = (href: string) => {\n    const element = document.querySelector(href);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n    setIsMobileMenuOpen(false);\n  };\n\n  return (\n    <header\n      className={cn(\n        'fixed top-0 left-0 right-0 z-50 transition-all duration-300',\n        isScrolled\n          ? 'bg-white/90 backdrop-blur-md shadow-lg dark:bg-gray-900/90'\n          : 'bg-transparent'\n      )}\n    >\n      <nav className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <div className=\"flex-shrink-0\">\n            <Link\n              href=\"#home\"\n              onClick={(e) => {\n                e.preventDefault();\n                scrollToSection('#home');\n              }}\n              className=\"text-2xl font-bold text-gray-900 dark:text-white\"\n            >\n              Portfolio\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-4\">\n              {navigation.map((item) => (\n                <button\n                  key={item.name}\n                  onClick={() => scrollToSection(item.href)}\n                  className=\"rounded-md px-3 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white\"\n                >\n                  {item.name}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"inline-flex items-center justify-center rounded-md p-2 text-gray-700 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white\"\n            >\n              {isMobileMenuOpen ? (\n                <X className=\"h-6 w-6\" />\n              ) : (\n                <Menu className=\"h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMobileMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"space-y-1 px-2 pb-3 pt-2 sm:px-3\">\n              {navigation.map((item) => (\n                <button\n                  key={item.name}\n                  onClick={() => scrollToSection(item.href)}\n                  className=\"block w-full rounded-md px-3 py-2 text-left text-base font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white\"\n                >\n                  {item.name}\n                </button>\n              ))}\n            </div>\n          </div>\n        )}\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;;;AALA;;;;;AAOA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAU,MAAM;IAAU;IAClC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAc,MAAM;IAAc;IAC1C;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEc,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;QACA,oBAAoB;IACtB;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA,aACI,+DACA;kBAGN,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,SAAS,CAAC;oCACR,EAAE,cAAc;oCAChB,gBAAgB;gCAClB;gCACA,WAAU;0CACX;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;wCAEC,SAAS,IAAM,gBAAgB,KAAK,IAAI;wCACxC,WAAU;kDAET,KAAK,IAAI;uCAJL,KAAK,IAAI;;;;;;;;;;;;;;;sCAWtB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,oBAAoB,CAAC;gCACpC,WAAU;0CAET,iCACC,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAEb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAOvB,kCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;gCAEC,SAAS,IAAM,gBAAgB,KAAK,IAAI;gCACxC,WAAU;0CAET,KAAK,IAAI;+BAJL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAahC;GA/FwB;KAAA", "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/BoxSync/2.%20Univeristy/5.%20Lincoln%20University/3.%20MAC/4.%20Portfolio/portfolio_webapp/portfolio/src/components/ui/AnimatedSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, ReactNode } from 'react';\nimport { useInView } from 'react-intersection-observer';\n\ninterface AnimatedSectionProps {\n  children: ReactNode;\n  className?: string;\n  delay?: number;\n}\n\nexport default function AnimatedSection({ \n  children, \n  className = '', \n  delay = 0 \n}: AnimatedSectionProps) {\n  const { ref, inView } = useInView({\n    threshold: 0.1,\n    triggerOnce: true,\n  });\n\n  return (\n    <div\n      ref={ref}\n      className={`transition-all duration-1000 ${\n        inView \n          ? 'opacity-100 translate-y-0' \n          : 'opacity-0 translate-y-8'\n      } ${className}`}\n      style={{ transitionDelay: `${delay}ms` }}\n    >\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;;;AAHA;;AAWe,SAAS,gBAAgB,EACtC,QAAQ,EACR,YAAY,EAAE,EACd,QAAQ,CAAC,EACY;;IACrB,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;QAChC,WAAW;QACX,aAAa;IACf;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAC,6BAA6B,EACvC,SACI,8BACA,0BACL,CAAC,EAAE,WAAW;QACf,OAAO;YAAE,iBAAiB,GAAG,MAAM,EAAE,CAAC;QAAC;kBAEtC;;;;;;AAGP;GAvBwB;;QAKE,sKAAA,CAAA,YAAS;;;KALX", "debugId": null}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/BoxSync/2.%20Univeristy/5.%20Lincoln%20University/3.%20MAC/4.%20Portfolio/portfolio_webapp/portfolio/src/components/sections/SkillsSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport AnimatedSection from '@/components/ui/AnimatedSection';\nimport { Cloud, Code, Database, Shield, Smartphone, Globe } from 'lucide-react';\n\n// Define skill categories and their data\nconst skillCategories = [\n  {\n    id: 'cloud',\n    name: 'Cloud Technologies',\n    icon: Cloud,\n    color: 'bg-blue-500',\n    skills: [\n      { name: 'AWS', level: 85, description: 'EC2, S3, Lambda, RDS' },\n      { name: 'Microsoft Azure', level: 80, description: 'App Service, Functions, Storage' },\n      { name: 'Google Cloud', level: 70, description: 'Compute Engine, Cloud Storage' },\n      { name: 'Docker', level: 75, description: 'Containerization & Orchestration' },\n      { name: 'Kubernetes', level: 65, description: 'Container Orchestration' },\n    ],\n  },\n  {\n    id: 'frontend',\n    name: 'Frontend Development',\n    icon: Globe,\n    color: 'bg-green-500',\n    skills: [\n      { name: 'React', level: 90, description: 'Hooks, Context, Redux' },\n      { name: 'TypeScript', level: 85, description: 'Type Safety & Modern JS' },\n      { name: 'Next.js', level: 80, description: 'SSR, SSG, API Routes' },\n      { name: 'Tailwind CSS', level: 85, description: 'Utility-First Styling' },\n      { name: 'JavaScript', level: 90, description: 'ES6+, Async/Await' },\n    ],\n  },\n  {\n    id: 'backend',\n    name: 'Backend Development',\n    icon: Code,\n    color: 'bg-purple-500',\n    skills: [\n      { name: 'Node.js', level: 80, description: 'Express, APIs, Microservices' },\n      { name: 'Python', level: 85, description: 'Django, Flask, FastAPI' },\n      { name: 'C#/.NET', level: 75, description: 'ASP.NET Core, Web APIs' },\n      { name: 'REST APIs', level: 85, description: 'RESTful Services Design' },\n      { name: 'GraphQL', level: 70, description: 'Query Language & Runtime' },\n    ],\n  },\n  {\n    id: 'data',\n    name: 'Data Analytics',\n    icon: Database,\n    color: 'bg-orange-500',\n    skills: [\n      { name: 'SQL', level: 85, description: 'PostgreSQL, MySQL, SQL Server' },\n      { name: 'Python Data Science', level: 80, description: 'Pandas, NumPy, Matplotlib' },\n      { name: 'Power BI', level: 75, description: 'Data Visualization & Reports' },\n      { name: 'Tableau', level: 70, description: 'Business Intelligence' },\n      { name: 'MongoDB', level: 75, description: 'NoSQL Database Design' },\n    ],\n  },\n  {\n    id: 'security',\n    name: 'Cybersecurity',\n    icon: Shield,\n    color: 'bg-red-500',\n    skills: [\n      { name: 'Security Fundamentals', level: 75, description: 'OWASP, Security Principles' },\n      { name: 'Authentication', level: 80, description: 'JWT, OAuth, SSO' },\n      { name: 'Network Security', level: 70, description: 'Firewalls, VPN, SSL/TLS' },\n      { name: 'Penetration Testing', level: 65, description: 'Vulnerability Assessment' },\n      { name: 'Compliance', level: 70, description: 'ISO 27001, GDPR' },\n    ],\n  },\n  {\n    id: 'mobile',\n    name: 'Mobile Development',\n    icon: Smartphone,\n    color: 'bg-indigo-500',\n    skills: [\n      { name: 'React Native', level: 75, description: 'Cross-Platform Mobile Apps' },\n      { name: 'Flutter', level: 70, description: 'Dart, Cross-Platform UI' },\n      { name: 'Progressive Web Apps', level: 80, description: 'PWA, Service Workers' },\n      { name: 'Mobile UI/UX', level: 75, description: 'Responsive Design' },\n    ],\n  },\n];\n\ninterface SkillBarProps {\n  skill: {\n    name: string;\n    level: number;\n    description: string;\n  };\n  delay: number;\n}\n\nfunction SkillBar({ skill, delay }: SkillBarProps) {\n  return (\n    <AnimatedSection delay={delay} className=\"mb-4\">\n      <div className=\"flex justify-between items-center mb-2\">\n        <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n          {skill.name}\n        </span>\n        <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n          {skill.level}%\n        </span>\n      </div>\n      <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-1\">\n        <div\n          className=\"bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full transition-all duration-1000 ease-out\"\n          style={{\n            width: `${skill.level}%`,\n            transitionDelay: `${delay}ms`,\n          }}\n        />\n      </div>\n      <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n        {skill.description}\n      </p>\n    </AnimatedSection>\n  );\n}\n\nexport default function SkillsSection() {\n  const [activeCategory, setActiveCategory] = useState('cloud');\n\n  const activeSkills = skillCategories.find(cat => cat.id === activeCategory)?.skills || [];\n\n  return (\n    <section id=\"skills\" className=\"py-20 bg-gray-50 dark:bg-gray-800\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <AnimatedSection className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n            Technical Skills & Expertise\n          </h2>\n          <p className=\"text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\">\n            Comprehensive technical skills across cloud technologies, full-stack development, \n            data analytics, and cybersecurity - aligned with New Zealand's tech industry demands.\n          </p>\n        </AnimatedSection>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Category Selector */}\n          <AnimatedSection delay={200} className=\"lg:col-span-1\">\n            <div className=\"bg-white dark:bg-gray-900 rounded-xl shadow-lg p-6\">\n              <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6\">\n                Skill Categories\n              </h3>\n              <div className=\"space-y-2\">\n                {skillCategories.map((category) => {\n                  const IconComponent = category.icon;\n                  return (\n                    <button\n                      key={category.id}\n                      onClick={() => setActiveCategory(category.id)}\n                      className={`w-full flex items-center space-x-3 p-3 rounded-lg transition-all duration-300 ${\n                        activeCategory === category.id\n                          ? 'bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-500'\n                          : 'hover:bg-gray-50 dark:hover:bg-gray-800'\n                      }`}\n                    >\n                      <div className={`p-2 rounded-lg ${category.color} text-white`}>\n                        <IconComponent className=\"w-4 h-4\" />\n                      </div>\n                      <span className={`font-medium ${\n                        activeCategory === category.id\n                          ? 'text-blue-600 dark:text-blue-400'\n                          : 'text-gray-700 dark:text-gray-300'\n                      }`}>\n                        {category.name}\n                      </span>\n                    </button>\n                  );\n                })}\n              </div>\n            </div>\n          </AnimatedSection>\n\n          {/* Skills Display */}\n          <AnimatedSection delay={400} className=\"lg:col-span-2\">\n            <div className=\"bg-white dark:bg-gray-900 rounded-xl shadow-lg p-6\">\n              <div className=\"flex items-center space-x-3 mb-6\">\n                {(() => {\n                  const activeCategory_data = skillCategories.find(cat => cat.id === activeCategory);\n                  if (!activeCategory_data) return null;\n                  const IconComponent = activeCategory_data.icon;\n                  return (\n                    <>\n                      <div className={`p-3 rounded-lg ${activeCategory_data.color} text-white`}>\n                        <IconComponent className=\"w-6 h-6\" />\n                      </div>\n                      <h3 className=\"text-2xl font-semibold text-gray-900 dark:text-white\">\n                        {activeCategory_data.name}\n                      </h3>\n                    </>\n                  );\n                })()}\n              </div>\n              \n              <div className=\"space-y-6\">\n                {activeSkills.map((skill, index) => (\n                  <SkillBar\n                    key={skill.name}\n                    skill={skill}\n                    delay={600 + index * 100}\n                  />\n                ))}\n              </div>\n            </div>\n          </AnimatedSection>\n        </div>\n\n        {/* Quick Skills Overview */}\n        <AnimatedSection delay={800} className=\"mt-16\">\n          <div className=\"bg-white dark:bg-gray-900 rounded-xl shadow-lg p-8\">\n            <h3 className=\"text-2xl font-semibold text-gray-900 dark:text-white mb-6 text-center\">\n              Key Technologies at a Glance\n            </h3>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4\">\n              {['AWS', 'React', 'TypeScript', 'Python', 'Node.js', 'Docker', 'SQL', 'Azure', 'Next.js', 'MongoDB', 'Power BI', 'C#'].map((tech, index) => (\n                <div\n                  key={tech}\n                  className=\"bg-gray-50 dark:bg-gray-800 rounded-lg p-3 text-center hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors duration-300\"\n                  style={{ animationDelay: `${1000 + index * 50}ms` }}\n                >\n                  <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    {tech}\n                  </span>\n                </div>\n              ))}\n            </div>\n          </div>\n        </AnimatedSection>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAMA,yCAAyC;AACzC,MAAM,kBAAkB;IACtB;QACE,IAAI;QACJ,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,QAAQ;YACN;gBAAE,MAAM;gBAAO,OAAO;gBAAI,aAAa;YAAuB;YAC9D;gBAAE,MAAM;gBAAmB,OAAO;gBAAI,aAAa;YAAkC;YACrF;gBAAE,MAAM;gBAAgB,OAAO;gBAAI,aAAa;YAAgC;YAChF;gBAAE,MAAM;gBAAU,OAAO;gBAAI,aAAa;YAAmC;YAC7E;gBAAE,MAAM;gBAAc,OAAO;gBAAI,aAAa;YAA0B;SACzE;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,QAAQ;YACN;gBAAE,MAAM;gBAAS,OAAO;gBAAI,aAAa;YAAwB;YACjE;gBAAE,MAAM;gBAAc,OAAO;gBAAI,aAAa;YAA0B;YACxE;gBAAE,MAAM;gBAAW,OAAO;gBAAI,aAAa;YAAuB;YAClE;gBAAE,MAAM;gBAAgB,OAAO;gBAAI,aAAa;YAAwB;YACxE;gBAAE,MAAM;gBAAc,OAAO;gBAAI,aAAa;YAAoB;SACnE;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM,qMAAA,CAAA,OAAI;QACV,OAAO;QACP,QAAQ;YACN;gBAAE,MAAM;gBAAW,OAAO;gBAAI,aAAa;YAA+B;YAC1E;gBAAE,MAAM;gBAAU,OAAO;gBAAI,aAAa;YAAyB;YACnE;gBAAE,MAAM;gBAAW,OAAO;gBAAI,aAAa;YAAyB;YACpE;gBAAE,MAAM;gBAAa,OAAO;gBAAI,aAAa;YAA0B;YACvE;gBAAE,MAAM;gBAAW,OAAO;gBAAI,aAAa;YAA2B;SACvE;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;QACd,OAAO;QACP,QAAQ;YACN;gBAAE,MAAM;gBAAO,OAAO;gBAAI,aAAa;YAAgC;YACvE;gBAAE,MAAM;gBAAuB,OAAO;gBAAI,aAAa;YAA4B;YACnF;gBAAE,MAAM;gBAAY,OAAO;gBAAI,aAAa;YAA+B;YAC3E;gBAAE,MAAM;gBAAW,OAAO;gBAAI,aAAa;YAAwB;YACnE;gBAAE,MAAM;gBAAW,OAAO;gBAAI,aAAa;YAAwB;SACpE;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM,yMAAA,CAAA,SAAM;QACZ,OAAO;QACP,QAAQ;YACN;gBAAE,MAAM;gBAAyB,OAAO;gBAAI,aAAa;YAA6B;YACtF;gBAAE,MAAM;gBAAkB,OAAO;gBAAI,aAAa;YAAkB;YACpE;gBAAE,MAAM;gBAAoB,OAAO;gBAAI,aAAa;YAA0B;YAC9E;gBAAE,MAAM;gBAAuB,OAAO;gBAAI,aAAa;YAA2B;YAClF;gBAAE,MAAM;gBAAc,OAAO;gBAAI,aAAa;YAAkB;SACjE;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM,iNAAA,CAAA,aAAU;QAChB,OAAO;QACP,QAAQ;YACN;gBAAE,MAAM;gBAAgB,OAAO;gBAAI,aAAa;YAA6B;YAC7E;gBAAE,MAAM;gBAAW,OAAO;gBAAI,aAAa;YAA0B;YACrE;gBAAE,MAAM;gBAAwB,OAAO;gBAAI,aAAa;YAAuB;YAC/E;gBAAE,MAAM;gBAAgB,OAAO;gBAAI,aAAa;YAAoB;SACrE;IACH;CACD;AAWD,SAAS,SAAS,EAAE,KAAK,EAAE,KAAK,EAAiB;IAC/C,qBACE,6LAAC,8IAAA,CAAA,UAAe;QAAC,OAAO;QAAO,WAAU;;0BACvC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCACb,MAAM,IAAI;;;;;;kCAEb,6LAAC;wBAAK,WAAU;;4BACb,MAAM,KAAK;4BAAC;;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBACL,OAAO,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC;wBACxB,iBAAiB,GAAG,MAAM,EAAE,CAAC;oBAC/B;;;;;;;;;;;0BAGJ,6LAAC;gBAAE,WAAU;0BACV,MAAM,WAAW;;;;;;;;;;;;AAI1B;KAzBS;AA2BM,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,eAAe,gBAAgB,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,iBAAiB,UAAU,EAAE;IAEzF,qBACE,6LAAC;QAAQ,IAAG;QAAS,WAAU;kBAC7B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,8IAAA,CAAA,UAAe;oBAAC,WAAU;;sCACzB,6LAAC;4BAAG,WAAU;sCAAoE;;;;;;sCAGlF,6LAAC;4BAAE,WAAU;sCAA6D;;;;;;;;;;;;8BAM5E,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,8IAAA,CAAA,UAAe;4BAAC,OAAO;4BAAK,WAAU;sCACrC,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAGzE,6LAAC;wCAAI,WAAU;kDACZ,gBAAgB,GAAG,CAAC,CAAC;4CACpB,MAAM,gBAAgB,SAAS,IAAI;4CACnC,qBACE,6LAAC;gDAEC,SAAS,IAAM,kBAAkB,SAAS,EAAE;gDAC5C,WAAW,CAAC,8EAA8E,EACxF,mBAAmB,SAAS,EAAE,GAC1B,8DACA,2CACJ;;kEAEF,6LAAC;wDAAI,WAAW,CAAC,eAAe,EAAE,SAAS,KAAK,CAAC,WAAW,CAAC;kEAC3D,cAAA,6LAAC;4DAAc,WAAU;;;;;;;;;;;kEAE3B,6LAAC;wDAAK,WAAW,CAAC,YAAY,EAC5B,mBAAmB,SAAS,EAAE,GAC1B,qCACA,oCACJ;kEACC,SAAS,IAAI;;;;;;;+CAhBX,SAAS,EAAE;;;;;wCAoBtB;;;;;;;;;;;;;;;;;sCAMN,6LAAC,8IAAA,CAAA,UAAe;4BAAC,OAAO;4BAAK,WAAU;sCACrC,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,CAAC;4CACA,MAAM,sBAAsB,gBAAgB,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;4CACnE,IAAI,CAAC,qBAAqB,OAAO;4CACjC,MAAM,gBAAgB,oBAAoB,IAAI;4CAC9C,qBACE;;kEACE,6LAAC;wDAAI,WAAW,CAAC,eAAe,EAAE,oBAAoB,KAAK,CAAC,WAAW,CAAC;kEACtE,cAAA,6LAAC;4DAAc,WAAU;;;;;;;;;;;kEAE3B,6LAAC;wDAAG,WAAU;kEACX,oBAAoB,IAAI;;;;;;;;wCAIjC,CAAC;;;;;;kDAGH,6LAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC,OAAO,sBACxB,6LAAC;gDAEC,OAAO;gDACP,OAAO,MAAM,QAAQ;+CAFhB,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAW3B,6LAAC,8IAAA,CAAA,UAAe;oBAAC,OAAO;oBAAK,WAAU;8BACrC,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwE;;;;;;0CAGtF,6LAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAO;oCAAS;oCAAc;oCAAU;oCAAW;oCAAU;oCAAO;oCAAS;oCAAW;oCAAW;oCAAY;iCAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAChI,6LAAC;wCAEC,WAAU;wCACV,OAAO;4CAAE,gBAAgB,GAAG,OAAO,QAAQ,GAAG,EAAE,CAAC;wCAAC;kDAElD,cAAA,6LAAC;4CAAK,WAAU;sDACb;;;;;;uCALE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAevB;GAjHwB;MAAA", "debugId": null}}, {"offset": {"line": 823, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/BoxSync/2.%20Univeristy/5.%20Lincoln%20University/3.%20MAC/4.%20Portfolio/portfolio_webapp/portfolio/src/data/projects.ts"], "sourcesContent": ["export interface Project {\n  id: string\n  title: string\n  description: string\n  longDescription: string\n  technologies: string[]\n  category: 'web' | 'mobile' | 'data' | 'cloud' | 'security' | 'automation'\n  image: string\n  liveUrl?: string\n  githubUrl?: string\n  featured: boolean\n  status: 'completed' | 'in-progress' | 'planned'\n  impact?: string\n  challenges?: string[]\n  learnings?: string[]\n}\n\nexport const projects: Project[] = [\n  {\n    id: 'smart-farm-management',\n    title: 'Smart Farm Management System',\n    description: 'IoT-enabled farm management platform for New Zealand agriculture with real-time monitoring and predictive analytics.',\n    longDescription: 'A comprehensive farm management system that integrates IoT sensors, weather data, and machine learning to optimize crop yields and resource usage. Built specifically for New Zealand farmers to monitor soil conditions, irrigation systems, livestock health, and weather patterns. The system provides actionable insights to improve productivity and sustainability.',\n    technologies: ['React', 'TypeScript', 'Node.js', 'PostgreSQL', 'AWS IoT Core', 'Python', 'TensorFlow', 'Docker', 'AWS Lambda'],\n    category: 'web',\n    image: '/images/projects/farm-management.jpg',\n    liveUrl: 'https://smart-farm-demo.vercel.app',\n    githubUrl: 'https://github.com/yourusername/smart-farm-management',\n    featured: true,\n    status: 'completed',\n    impact: 'Improved farm efficiency by 25% and reduced water usage by 30% in pilot testing',\n    challenges: ['Integrating diverse IoT sensors', 'Handling unreliable rural internet connectivity', 'Creating intuitive UI for non-technical users'],\n    learnings: ['IoT data processing at scale', 'Agricultural domain knowledge', 'Offline-first application design']\n  },\n  {\n    id: 'nz-tourism-booking',\n    title: 'NZ Tourism Experience Platform',\n    description: 'Mobile-first booking platform for unique New Zealand tourism experiences with real-time availability and local guide integration.',\n    longDescription: 'A React Native mobile application that connects tourists with authentic New Zealand experiences. Features include real-time booking, payment processing, location-based recommendations, weather integration, and direct communication with local guides. Supports both individual travelers and tour operators.',\n    technologies: ['React Native', 'TypeScript', 'Firebase', 'Stripe API', 'Google Maps API', 'Weather API', 'Push Notifications'],\n    category: 'mobile',\n    image: '/images/projects/tourism-platform.jpg',\n    liveUrl: 'https://nz-experiences-app.com',\n    githubUrl: 'https://github.com/yourusername/nz-tourism-platform',\n    featured: true,\n    status: 'completed',\n    impact: 'Connected 500+ tourists with local experiences, generating $50K+ in bookings',\n    challenges: ['Cross-platform compatibility', 'Real-time availability synchronization', 'Payment processing compliance'],\n    learnings: ['Mobile app development lifecycle', 'Payment gateway integration', 'User experience design for tourism']\n  },\n  {\n    id: 'business-analytics-dashboard',\n    title: 'SME Business Intelligence Dashboard',\n    description: 'Comprehensive analytics dashboard for New Zealand small-medium enterprises with automated reporting and insights.',\n    longDescription: 'A powerful business intelligence platform designed for New Zealand SMEs to track KPIs, analyze customer behavior, and generate automated reports. Integrates with popular accounting software, e-commerce platforms, and social media APIs to provide holistic business insights.',\n    technologies: ['Next.js', 'TypeScript', 'Python', 'PostgreSQL', 'Redis', 'D3.js', 'Chart.js', 'Pandas', 'FastAPI'],\n    category: 'data',\n    image: '/images/projects/analytics-dashboard.jpg',\n    liveUrl: 'https://sme-analytics-demo.vercel.app',\n    githubUrl: 'https://github.com/yourusername/sme-analytics-dashboard',\n    featured: true,\n    status: 'completed',\n    impact: 'Helped 50+ SMEs increase revenue by average of 15% through data-driven decisions',\n    challenges: ['Data integration from multiple sources', 'Real-time data processing', 'Creating intuitive visualizations'],\n    learnings: ['Data pipeline architecture', 'Business intelligence concepts', 'API integration patterns']\n  },\n  {\n    id: 'cloud-infrastructure-automation',\n    title: 'Cloud Infrastructure Automation Suite',\n    description: 'Infrastructure as Code solution for automated AWS deployment with monitoring, scaling, and cost optimization.',\n    longDescription: 'A comprehensive Infrastructure as Code solution that automates AWS resource provisioning, monitoring, and cost optimization. Includes Terraform modules, CI/CD pipelines, automated scaling policies, and comprehensive monitoring dashboards.',\n    technologies: ['Terraform', 'AWS', 'Docker', 'Kubernetes', 'GitLab CI/CD', 'Prometheus', 'Grafana', 'Python', 'Bash'],\n    category: 'cloud',\n    image: '/images/projects/cloud-automation.jpg',\n    githubUrl: 'https://github.com/yourusername/cloud-infrastructure-automation',\n    featured: true,\n    status: 'completed',\n    impact: 'Reduced infrastructure deployment time by 80% and operational costs by 40%',\n    challenges: ['Complex multi-environment deployments', 'Cost optimization strategies', 'Security compliance automation'],\n    learnings: ['Infrastructure as Code best practices', 'Cloud cost optimization', 'DevOps automation patterns']\n  },\n  {\n    id: 'cybersecurity-monitoring',\n    title: 'Enterprise Security Monitoring System',\n    description: 'Real-time cybersecurity monitoring and threat detection system with automated incident response capabilities.',\n    longDescription: 'An enterprise-grade security monitoring system that provides real-time threat detection, automated incident response, and compliance reporting. Features machine learning-based anomaly detection, integration with SIEM tools, and automated remediation workflows.',\n    technologies: ['Python', 'Elasticsearch', 'Kibana', 'Docker', 'Redis', 'PostgreSQL', 'Scikit-learn', 'FastAPI', 'Celery'],\n    category: 'security',\n    image: '/images/projects/security-monitoring.jpg',\n    githubUrl: 'https://github.com/yourusername/security-monitoring-system',\n    featured: false,\n    status: 'completed',\n    impact: 'Detected and prevented 95% of security threats in testing environment',\n    challenges: ['Real-time log processing at scale', 'False positive reduction', 'Integration with existing security tools'],\n    learnings: ['Cybersecurity best practices', 'Machine learning for anomaly detection', 'SIEM integration patterns']\n  },\n  {\n    id: 'workflow-automation-platform',\n    title: 'Business Process Automation Platform',\n    description: 'No-code workflow automation platform for New Zealand businesses to streamline operations and reduce manual tasks.',\n    longDescription: 'A user-friendly platform that allows businesses to create automated workflows without coding. Features drag-and-drop workflow builder, integration with popular business tools, and comprehensive analytics on process efficiency.',\n    technologies: ['Vue.js', 'TypeScript', 'Node.js', 'MongoDB', 'Redis', 'Docker', 'Zapier API', 'Slack API', 'Email APIs'],\n    category: 'automation',\n    image: '/images/projects/automation-platform.jpg',\n    liveUrl: 'https://workflow-automation-demo.com',\n    githubUrl: 'https://github.com/yourusername/workflow-automation-platform',\n    featured: false,\n    status: 'in-progress',\n    impact: 'Saved businesses an average of 10 hours per week on manual tasks',\n    challenges: ['Creating intuitive no-code interface', 'Reliable workflow execution', 'Third-party API integration'],\n    learnings: ['Workflow engine design', 'No-code platform development', 'Business process optimization']\n  }\n]\n\nexport const projectCategories = [\n  { id: 'all', name: 'All Projects', icon: '🚀' },\n  { id: 'web', name: 'Web Applications', icon: '🌐' },\n  { id: 'mobile', name: 'Mobile Apps', icon: '📱' },\n  { id: 'data', name: 'Data Analytics', icon: '📊' },\n  { id: 'cloud', name: 'Cloud Solutions', icon: '☁️' },\n  { id: 'security', name: 'Cybersecurity', icon: '🔒' },\n  { id: 'automation', name: 'Automation', icon: '⚙️' }\n]\n\nexport const getTechnologyColor = (tech: string): string => {\n  const colorMap: { [key: string]: string } = {\n    'React': 'bg-blue-100 text-blue-800',\n    'TypeScript': 'bg-blue-100 text-blue-800',\n    'Node.js': 'bg-green-100 text-green-800',\n    'Python': 'bg-yellow-100 text-yellow-800',\n    'AWS': 'bg-orange-100 text-orange-800',\n    'Docker': 'bg-blue-100 text-blue-800',\n    'PostgreSQL': 'bg-blue-100 text-blue-800',\n    'MongoDB': 'bg-green-100 text-green-800',\n    'Firebase': 'bg-yellow-100 text-yellow-800',\n    'React Native': 'bg-blue-100 text-blue-800',\n    'Vue.js': 'bg-green-100 text-green-800',\n    'Next.js': 'bg-gray-100 text-gray-800',\n    'Terraform': 'bg-purple-100 text-purple-800',\n    'Kubernetes': 'bg-blue-100 text-blue-800',\n    'TensorFlow': 'bg-orange-100 text-orange-800',\n    'FastAPI': 'bg-green-100 text-green-800',\n    'Elasticsearch': 'bg-yellow-100 text-yellow-800',\n    'Redis': 'bg-red-100 text-red-800'\n  }\n  \n  return colorMap[tech] || 'bg-gray-100 text-gray-800'\n}\n"], "names": [], "mappings": ";;;;;AAiBO,MAAM,WAAsB;IACjC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,cAAc;YAAC;YAAS;YAAc;YAAW;YAAc;YAAgB;YAAU;YAAc;YAAU;SAAa;QAC9H,UAAU;QACV,OAAO;QACP,SAAS;QACT,WAAW;QACX,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,YAAY;YAAC;YAAmC;YAAmD;SAAgD;QACnJ,WAAW;YAAC;YAAgC;YAAiC;SAAmC;IAClH;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,cAAc;YAAC;YAAgB;YAAc;YAAY;YAAc;YAAmB;YAAe;SAAqB;QAC9H,UAAU;QACV,OAAO;QACP,SAAS;QACT,WAAW;QACX,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,YAAY;YAAC;YAAgC;YAA0C;SAAgC;QACvH,WAAW;YAAC;YAAoC;YAA+B;SAAqC;IACtH;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,cAAc;YAAC;YAAW;YAAc;YAAU;YAAc;YAAS;YAAS;YAAY;YAAU;SAAU;QAClH,UAAU;QACV,OAAO;QACP,SAAS;QACT,WAAW;QACX,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,YAAY;YAAC;YAA0C;YAA6B;SAAoC;QACxH,WAAW;YAAC;YAA8B;YAAkC;SAA2B;IACzG;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,cAAc;YAAC;YAAa;YAAO;YAAU;YAAc;YAAgB;YAAc;YAAW;YAAU;SAAO;QACrH,UAAU;QACV,OAAO;QACP,WAAW;QACX,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,YAAY;YAAC;YAAyC;YAAgC;SAAiC;QACvH,WAAW;YAAC;YAAyC;YAA2B;SAA6B;IAC/G;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,cAAc;YAAC;YAAU;YAAiB;YAAU;YAAU;YAAS;YAAc;YAAgB;YAAW;SAAS;QACzH,UAAU;QACV,OAAO;QACP,WAAW;QACX,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,YAAY;YAAC;YAAqC;YAA4B;SAA2C;QACzH,WAAW;YAAC;YAAgC;YAA0C;SAA4B;IACpH;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,cAAc;YAAC;YAAU;YAAc;YAAW;YAAW;YAAS;YAAU;YAAc;YAAa;SAAa;QACxH,UAAU;QACV,OAAO;QACP,SAAS;QACT,WAAW;QACX,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,YAAY;YAAC;YAAwC;YAA+B;SAA8B;QAClH,WAAW;YAAC;YAA0B;YAAgC;SAAgC;IACxG;CACD;AAEM,MAAM,oBAAoB;IAC/B;QAAE,IAAI;QAAO,MAAM;QAAgB,MAAM;IAAK;IAC9C;QAAE,IAAI;QAAO,MAAM;QAAoB,MAAM;IAAK;IAClD;QAAE,IAAI;QAAU,MAAM;QAAe,MAAM;IAAK;IAChD;QAAE,IAAI;QAAQ,MAAM;QAAkB,MAAM;IAAK;IACjD;QAAE,IAAI;QAAS,MAAM;QAAmB,MAAM;IAAK;IACnD;QAAE,IAAI;QAAY,MAAM;QAAiB,MAAM;IAAK;IACpD;QAAE,IAAI;QAAc,MAAM;QAAc,MAAM;IAAK;CACpD;AAEM,MAAM,qBAAqB,CAAC;IACjC,MAAM,WAAsC;QAC1C,SAAS;QACT,cAAc;QACd,WAAW;QACX,UAAU;QACV,OAAO;QACP,UAAU;QACV,cAAc;QACd,WAAW;QACX,YAAY;QACZ,gBAAgB;QAChB,UAAU;QACV,WAAW;QACX,aAAa;QACb,cAAc;QACd,cAAc;QACd,WAAW;QACX,iBAAiB;QACjB,SAAS;IACX;IAEA,OAAO,QAAQ,CAAC,KAAK,IAAI;AAC3B", "debugId": null}}, {"offset": {"line": 1099, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/BoxSync/2.%20Univeristy/5.%20Lincoln%20University/3.%20MAC/4.%20Portfolio/portfolio_webapp/portfolio/src/components/ui/ProjectCard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { ExternalLink, Github, Eye, Code, TrendingUp, AlertCircle } from 'lucide-react';\nimport { Project, getTechnologyColor } from '@/data/projects';\nimport AnimatedSection from './AnimatedSection';\n\ninterface ProjectCardProps {\n  project: Project;\n  delay?: number;\n}\n\nexport default function ProjectCard({ project, delay = 0 }: ProjectCardProps) {\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [imageError, setImageError] = useState(false);\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n      case 'in-progress':\n        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\n      case 'planned':\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n      default:\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return '✅';\n      case 'in-progress':\n        return '🚧';\n      case 'planned':\n        return '📋';\n      default:\n        return '📋';\n    }\n  };\n\n  return (\n    <AnimatedSection delay={delay}>\n      <div className=\"bg-white dark:bg-gray-900 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 overflow-hidden\">\n        {/* Project Image */}\n        <div className=\"relative h-48 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-700\">\n          {!imageError ? (\n            <img\n              src={project.image}\n              alt={project.title}\n              className=\"w-full h-full object-cover\"\n              onError={() => setImageError(true)}\n            />\n          ) : (\n            <div className=\"w-full h-full flex items-center justify-center\">\n              <div className=\"text-center text-gray-500 dark:text-gray-400\">\n                <div className=\"w-16 h-16 mx-auto mb-2 bg-gray-200 dark:bg-gray-600 rounded-lg flex items-center justify-center\">\n                  <Code className=\"w-8 h-8\" />\n                </div>\n                <p className=\"text-sm font-medium\">{project.title}</p>\n              </div>\n            </div>\n          )}\n          \n          {/* Status Badge */}\n          <div className=\"absolute top-4 left-4\">\n            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>\n              <span className=\"mr-1\">{getStatusIcon(project.status)}</span>\n              {project.status.charAt(0).toUpperCase() + project.status.slice(1).replace('-', ' ')}\n            </span>\n          </div>\n\n          {/* Featured Badge */}\n          {project.featured && (\n            <div className=\"absolute top-4 right-4\">\n              <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200\">\n                ⭐ Featured\n              </span>\n            </div>\n          )}\n        </div>\n\n        {/* Project Content */}\n        <div className=\"p-6\">\n          {/* Title and Category */}\n          <div className=\"flex items-start justify-between mb-3\">\n            <h3 className=\"text-xl font-bold text-gray-900 dark:text-white line-clamp-2\">\n              {project.title}\n            </h3>\n            <span className=\"ml-2 px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded-full text-xs font-medium whitespace-nowrap\">\n              {project.category}\n            </span>\n          </div>\n\n          {/* Description */}\n          <p className=\"text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-3\">\n            {project.description}\n          </p>\n\n          {/* Impact */}\n          {project.impact && (\n            <div className=\"mb-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\">\n              <div className=\"flex items-start space-x-2\">\n                <TrendingUp className=\"w-4 h-4 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0\" />\n                <p className=\"text-sm text-green-700 dark:text-green-300\">\n                  <strong>Impact:</strong> {project.impact}\n                </p>\n              </div>\n            </div>\n          )}\n\n          {/* Technologies */}\n          <div className=\"mb-4\">\n            <div className=\"flex flex-wrap gap-1\">\n              {project.technologies.slice(0, 4).map((tech) => (\n                <span\n                  key={tech}\n                  className={`px-2 py-1 rounded-full text-xs font-medium ${getTechnologyColor(tech)}`}\n                >\n                  {tech}\n                </span>\n              ))}\n              {project.technologies.length > 4 && (\n                <span className=\"px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300\">\n                  +{project.technologies.length - 4} more\n                </span>\n              )}\n            </div>\n          </div>\n\n          {/* Expanded Content */}\n          {isExpanded && (\n            <div className=\"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n              <div className=\"space-y-4\">\n                {/* Long Description */}\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 dark:text-white mb-2\">About This Project</h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                    {project.longDescription}\n                  </p>\n                </div>\n\n                {/* All Technologies */}\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 dark:text-white mb-2\">Technologies Used</h4>\n                  <div className=\"flex flex-wrap gap-1\">\n                    {project.technologies.map((tech) => (\n                      <span\n                        key={tech}\n                        className={`px-2 py-1 rounded-full text-xs font-medium ${getTechnologyColor(tech)}`}\n                      >\n                        {tech}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Challenges */}\n                {project.challenges && project.challenges.length > 0 && (\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900 dark:text-white mb-2 flex items-center\">\n                      <AlertCircle className=\"w-4 h-4 mr-1\" />\n                      Key Challenges\n                    </h4>\n                    <ul className=\"text-sm text-gray-600 dark:text-gray-300 space-y-1\">\n                      {project.challenges.map((challenge, index) => (\n                        <li key={index} className=\"flex items-start\">\n                          <span className=\"text-orange-500 mr-2\">•</span>\n                          {challenge}\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n\n                {/* Learnings */}\n                {project.learnings && project.learnings.length > 0 && (\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900 dark:text-white mb-2\">Key Learnings</h4>\n                    <ul className=\"text-sm text-gray-600 dark:text-gray-300 space-y-1\">\n                      {project.learnings.map((learning, index) => (\n                        <li key={index} className=\"flex items-start\">\n                          <span className=\"text-blue-500 mr-2\">•</span>\n                          {learning}\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n\n          {/* Action Buttons */}\n          <div className=\"flex items-center justify-between mt-6\">\n            <button\n              onClick={() => setIsExpanded(!isExpanded)}\n              className=\"flex items-center space-x-1 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm font-medium transition-colors\"\n            >\n              <Eye className=\"w-4 h-4\" />\n              <span>{isExpanded ? 'Show Less' : 'Learn More'}</span>\n            </button>\n\n            <div className=\"flex space-x-2\">\n              {project.githubUrl && (\n                <a\n                  href={project.githubUrl}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"flex items-center space-x-1 px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm\"\n                >\n                  <Github className=\"w-4 h-4\" />\n                  <span>Code</span>\n                </a>\n              )}\n              \n              {project.liveUrl && (\n                <a\n                  href={project.liveUrl}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"flex items-center space-x-1 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm\"\n                >\n                  <ExternalLink className=\"w-4 h-4\" />\n                  <span>Live Demo</span>\n                </a>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </AnimatedSection>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AALA;;;;;AAYe,SAAS,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAoB;;IAC1E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;g<PERSON><PERSON>,OAAO;YACT,KAAK;g<PERSON><PERSON>,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,8IAAA,CAAA,UAAe;QAAC,OAAO;kBACtB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;wBACZ,CAAC,2BACA,6LAAC;4BACC,KAAK,QAAQ,KAAK;4BAClB,KAAK,QAAQ,KAAK;4BAClB,WAAU;4BACV,SAAS,IAAM,cAAc;;;;;iDAG/B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,6LAAC;wCAAE,WAAU;kDAAuB,QAAQ,KAAK;;;;;;;;;;;;;;;;;sCAMvD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAW,CAAC,oEAAoE,EAAE,eAAe,QAAQ,MAAM,GAAG;;kDACtH,6LAAC;wCAAK,WAAU;kDAAQ,cAAc,QAAQ,MAAM;;;;;;oCACnD,QAAQ,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,QAAQ,MAAM,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK;;;;;;;;;;;;wBAKlF,QAAQ,QAAQ,kBACf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;0CAA4I;;;;;;;;;;;;;;;;;8BAQlK,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAEhB,6LAAC;oCAAK,WAAU;8CACb,QAAQ,QAAQ;;;;;;;;;;;;sCAKrB,6LAAC;4BAAE,WAAU;sCACV,QAAQ,WAAW;;;;;;wBAIrB,QAAQ,MAAM,kBACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,6LAAC;wCAAE,WAAU;;0DACX,6LAAC;0DAAO;;;;;;4CAAgB;4CAAE,QAAQ,MAAM;;;;;;;;;;;;;;;;;;sCAOhD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;oCACZ,QAAQ,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACrC,6LAAC;4CAEC,WAAW,CAAC,2CAA2C,EAAE,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;sDAElF;2CAHI;;;;;oCAMR,QAAQ,YAAY,CAAC,MAAM,GAAG,mBAC7B,6LAAC;wCAAK,WAAU;;4CAA2G;4CACvH,QAAQ,YAAY,CAAC,MAAM,GAAG;4CAAE;;;;;;;;;;;;;;;;;;wBAOzC,4BACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;0DACjE,6LAAC;gDAAE,WAAU;0DACV,QAAQ,eAAe;;;;;;;;;;;;kDAK5B,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;0DACjE,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,qBACzB,6LAAC;wDAEC,WAAW,CAAC,2CAA2C,EAAE,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;kEAElF;uDAHI;;;;;;;;;;;;;;;;oCAUZ,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,MAAM,GAAG,mBACjD,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAG1C,6LAAC;gDAAG,WAAU;0DACX,QAAQ,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBAClC,6LAAC;wDAAe,WAAU;;0EACxB,6LAAC;gEAAK,WAAU;0EAAuB;;;;;;4DACtC;;uDAFM;;;;;;;;;;;;;;;;oCAUhB,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,mBAC/C,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;0DACjE,6LAAC;gDAAG,WAAU;0DACX,QAAQ,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,sBAChC,6LAAC;wDAAe,WAAU;;0EACxB,6LAAC;gEAAK,WAAU;0EAAqB;;;;;;4DACpC;;uDAFM;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAavB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;;sDAEV,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,6LAAC;sDAAM,aAAa,cAAc;;;;;;;;;;;;8CAGpC,6LAAC;oCAAI,WAAU;;wCACZ,QAAQ,SAAS,kBAChB,6LAAC;4CACC,MAAM,QAAQ,SAAS;4CACvB,QAAO;4CACP,KAAI;4CACJ,WAAU;;8DAEV,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;8DAAK;;;;;;;;;;;;wCAIT,QAAQ,OAAO,kBACd,6LAAC;4CACC,MAAM,QAAQ,OAAO;4CACrB,QAAO;4CACP,KAAI;4CACJ,WAAU;;8DAEV,6LAAC,yNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;GA9NwB;KAAA", "debugId": null}}, {"offset": {"line": 1648, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/BoxSync/2.%20Univeristy/5.%20Lincoln%20University/3.%20MAC/4.%20Portfolio/portfolio_webapp/portfolio/src/components/sections/ProjectsSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useMemo } from 'react';\nimport { projects, projectCategories, Project } from '@/data/projects';\nimport ProjectCard from '@/components/ui/ProjectCard';\nimport AnimatedSection from '@/components/ui/AnimatedSection';\nimport { Search, Filter, Star, Code, TrendingUp } from 'lucide-react';\n\nexport default function ProjectsSection() {\n  const [activeCategory, setActiveCategory] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showFeaturedOnly, setShowFeaturedOnly] = useState(false);\n\n  // Filter projects based on category, search term, and featured status\n  const filteredProjects = useMemo(() => {\n    let filtered = projects;\n\n    // Filter by category\n    if (activeCategory !== 'all') {\n      filtered = filtered.filter(project => project.category === activeCategory);\n    }\n\n    // Filter by search term\n    if (searchTerm) {\n      filtered = filtered.filter(project =>\n        project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        project.technologies.some(tech => \n          tech.toLowerCase().includes(searchTerm.toLowerCase())\n        )\n      );\n    }\n\n    // Filter by featured status\n    if (showFeaturedOnly) {\n      filtered = filtered.filter(project => project.featured);\n    }\n\n    // Sort: featured first, then by status (completed first)\n    return filtered.sort((a, b) => {\n      if (a.featured && !b.featured) return -1;\n      if (!a.featured && b.featured) return 1;\n      if (a.status === 'completed' && b.status !== 'completed') return -1;\n      if (a.status !== 'completed' && b.status === 'completed') return 1;\n      return 0;\n    });\n  }, [activeCategory, searchTerm, showFeaturedOnly]);\n\n  const featuredProjects = projects.filter(project => project.featured);\n  const completedProjects = projects.filter(project => project.status === 'completed');\n\n  return (\n    <section id=\"projects\" className=\"py-20 bg-white dark:bg-gray-900\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <AnimatedSection className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n            Featured Projects & Solutions\n          </h2>\n          <p className=\"text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8\">\n            Real-world applications solving problems in agriculture, tourism, business automation, \n            and cloud infrastructure - designed for the New Zealand market and beyond.\n          </p>\n          \n          {/* Project Statistics */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-2xl mx-auto\">\n            <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4\">\n              <div className=\"flex items-center justify-center space-x-2 text-blue-600 dark:text-blue-400\">\n                <Code className=\"w-5 h-5\" />\n                <span className=\"text-2xl font-bold\">{projects.length}</span>\n              </div>\n              <p className=\"text-sm text-blue-700 dark:text-blue-300 mt-1\">Total Projects</p>\n            </div>\n            \n            <div className=\"bg-green-50 dark:bg-green-900/20 rounded-lg p-4\">\n              <div className=\"flex items-center justify-center space-x-2 text-green-600 dark:text-green-400\">\n                <Star className=\"w-5 h-5\" />\n                <span className=\"text-2xl font-bold\">{featuredProjects.length}</span>\n              </div>\n              <p className=\"text-sm text-green-700 dark:text-green-300 mt-1\">Featured Projects</p>\n            </div>\n            \n            <div className=\"bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4\">\n              <div className=\"flex items-center justify-center space-x-2 text-purple-600 dark:text-purple-400\">\n                <TrendingUp className=\"w-5 h-5\" />\n                <span className=\"text-2xl font-bold\">{completedProjects.length}</span>\n              </div>\n              <p className=\"text-sm text-purple-700 dark:text-purple-300 mt-1\">Completed</p>\n            </div>\n          </div>\n        </AnimatedSection>\n\n        {/* Filters and Search */}\n        <AnimatedSection delay={200} className=\"mb-12\">\n          <div className=\"bg-gray-50 dark:bg-gray-800 rounded-xl p-6\">\n            {/* Search Bar */}\n            <div className=\"mb-6\">\n              <div className=\"relative max-w-md mx-auto\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search projects or technologies...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                />\n              </div>\n            </div>\n\n            {/* Category Filters */}\n            <div className=\"flex flex-wrap justify-center gap-2 mb-4\">\n              {projectCategories.map((category) => (\n                <button\n                  key={category.id}\n                  onClick={() => setActiveCategory(category.id)}\n                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ${\n                    activeCategory === category.id\n                      ? 'bg-blue-600 text-white shadow-lg transform scale-105'\n                      : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-600'\n                  }`}\n                >\n                  <span>{category.icon}</span>\n                  <span>{category.name}</span>\n                </button>\n              ))}\n            </div>\n\n            {/* Additional Filters */}\n            <div className=\"flex justify-center\">\n              <button\n                onClick={() => setShowFeaturedOnly(!showFeaturedOnly)}\n                className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ${\n                  showFeaturedOnly\n                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'\n                    : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-yellow-50 dark:hover:bg-gray-600'\n                }`}\n              >\n                <Star className=\"w-4 h-4\" />\n                <span>Featured Only</span>\n              </button>\n            </div>\n          </div>\n        </AnimatedSection>\n\n        {/* Results Summary */}\n        <AnimatedSection delay={300} className=\"mb-8\">\n          <div className=\"text-center\">\n            <p className=\"text-gray-600 dark:text-gray-300\">\n              Showing <span className=\"font-semibold text-blue-600 dark:text-blue-400\">{filteredProjects.length}</span> \n              {' '}project{filteredProjects.length !== 1 ? 's' : ''}\n              {activeCategory !== 'all' && (\n                <span> in <span className=\"font-semibold\">{projectCategories.find(cat => cat.id === activeCategory)?.name}</span></span>\n              )}\n              {searchTerm && (\n                <span> matching \"<span className=\"font-semibold\">{searchTerm}</span>\"</span>\n              )}\n            </p>\n          </div>\n        </AnimatedSection>\n\n        {/* Projects Grid */}\n        {filteredProjects.length > 0 ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {filteredProjects.map((project, index) => (\n              <ProjectCard\n                key={project.id}\n                project={project}\n                delay={400 + index * 100}\n              />\n            ))}\n          </div>\n        ) : (\n          <AnimatedSection delay={400}>\n            <div className=\"text-center py-12\">\n              <div className=\"w-24 h-24 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center\">\n                <Filter className=\"w-12 h-12 text-gray-400\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n                No projects found\n              </h3>\n              <p className=\"text-gray-600 dark:text-gray-300 mb-4\">\n                Try adjusting your search terms or filters to find what you're looking for.\n              </p>\n              <button\n                onClick={() => {\n                  setSearchTerm('');\n                  setActiveCategory('all');\n                  setShowFeaturedOnly(false);\n                }}\n                className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n              >\n                Clear Filters\n              </button>\n            </div>\n          </AnimatedSection>\n        )}\n\n        {/* Call to Action */}\n        <AnimatedSection delay={600} className=\"mt-16 text-center\">\n          <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-8\">\n            <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n              Interested in Working Together?\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto\">\n              I'm always excited to work on new projects and solve challenging problems. \n              Let's discuss how we can bring your ideas to life.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <a\n                href=\"#contact\"\n                className=\"px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium\"\n              >\n                Get In Touch\n              </a>\n              <a\n                href=\"https://github.com/yourusername\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"px-8 py-3 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-600 hover:text-white transition-colors font-medium\"\n              >\n                View All Code\n              </a>\n            </div>\n          </div>\n        </AnimatedSection>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,sEAAsE;IACtE,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;qDAAE;YAC/B,IAAI,WAAW,0HAAA,CAAA,WAAQ;YAEvB,qBAAqB;YACrB,IAAI,mBAAmB,OAAO;gBAC5B,WAAW,SAAS,MAAM;iEAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;;YAC7D;YAEA,wBAAwB;YACxB,IAAI,YAAY;gBACd,WAAW,SAAS,MAAM;iEAAC,CAAA,UACzB,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3D,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACjE,QAAQ,YAAY,CAAC,IAAI;yEAAC,CAAA,OACxB,KAAK,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;;;YAGxD;YAEA,4BAA4B;YAC5B,IAAI,kBAAkB;gBACpB,WAAW,SAAS,MAAM;iEAAC,CAAA,UAAW,QAAQ,QAAQ;;YACxD;YAEA,yDAAyD;YACzD,OAAO,SAAS,IAAI;6DAAC,CAAC,GAAG;oBACvB,IAAI,EAAE,QAAQ,IAAI,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC;oBACvC,IAAI,CAAC,EAAE,QAAQ,IAAI,EAAE,QAAQ,EAAE,OAAO;oBACtC,IAAI,EAAE,MAAM,KAAK,eAAe,EAAE,MAAM,KAAK,aAAa,OAAO,CAAC;oBAClE,IAAI,EAAE,MAAM,KAAK,eAAe,EAAE,MAAM,KAAK,aAAa,OAAO;oBACjE,OAAO;gBACT;;QACF;oDAAG;QAAC;QAAgB;QAAY;KAAiB;IAEjD,MAAM,mBAAmB,0HAAA,CAAA,WAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ;IACpE,MAAM,oBAAoB,0HAAA,CAAA,WAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,MAAM,KAAK;IAExE,qBACE,6LAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,8IAAA,CAAA,UAAe;oBAAC,WAAU;;sCACzB,6LAAC;4BAAG,WAAU;sCAAoE;;;;;;sCAGlF,6LAAC;4BAAE,WAAU;sCAAkE;;;;;;sCAM/E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDAAK,WAAU;8DAAsB,0HAAA,CAAA,WAAQ,CAAC,MAAM;;;;;;;;;;;;sDAEvD,6LAAC;4CAAE,WAAU;sDAAgD;;;;;;;;;;;;8CAG/D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDAAK,WAAU;8DAAsB,iBAAiB,MAAM;;;;;;;;;;;;sDAE/D,6LAAC;4CAAE,WAAU;sDAAkD;;;;;;;;;;;;8CAGjE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,6LAAC;oDAAK,WAAU;8DAAsB,kBAAkB,MAAM;;;;;;;;;;;;sDAEhE,6LAAC;4CAAE,WAAU;sDAAoD;;;;;;;;;;;;;;;;;;;;;;;;8BAMvE,6LAAC,8IAAA,CAAA,UAAe;oBAAC,OAAO;oBAAK,WAAU;8BACrC,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;;;;;;0CAMhB,6LAAC;gCAAI,WAAU;0CACZ,0HAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,CAAC,yBACtB,6LAAC;wCAEC,SAAS,IAAM,kBAAkB,SAAS,EAAE;wCAC5C,WAAW,CAAC,yFAAyF,EACnG,mBAAmB,SAAS,EAAE,GAC1B,yDACA,sGACJ;;0DAEF,6LAAC;0DAAM,SAAS,IAAI;;;;;;0DACpB,6LAAC;0DAAM,SAAS,IAAI;;;;;;;uCATf,SAAS,EAAE;;;;;;;;;;0CAetB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS,IAAM,oBAAoB,CAAC;oCACpC,WAAW,CAAC,yFAAyF,EACnG,mBACI,0EACA,wGACJ;;sDAEF,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOd,6LAAC,8IAAA,CAAA,UAAe;oBAAC,OAAO;oBAAK,WAAU;8BACrC,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAAmC;8CACtC,6LAAC;oCAAK,WAAU;8CAAkD,iBAAiB,MAAM;;;;;;gCAChG;gCAAI;gCAAQ,iBAAiB,MAAM,KAAK,IAAI,MAAM;gCAClD,mBAAmB,uBAClB,6LAAC;;wCAAK;sDAAI,6LAAC;4CAAK,WAAU;sDAAiB,0HAAA,CAAA,oBAAiB,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,iBAAiB;;;;;;;;;;;;gCAEtG,4BACC,6LAAC;;wCAAK;sDAAW,6LAAC;4CAAK,WAAU;sDAAiB;;;;;;wCAAkB;;;;;;;;;;;;;;;;;;;;;;;gBAO3E,iBAAiB,MAAM,GAAG,kBACzB,6LAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,6LAAC,0IAAA,CAAA,UAAW;4BAEV,SAAS;4BACT,OAAO,MAAM,QAAQ;2BAFhB,QAAQ,EAAE;;;;;;;;;yCAOrB,6LAAC,8IAAA,CAAA,UAAe;oBAAC,OAAO;8BACtB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAEpB,6LAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;0CAGrD,6LAAC;gCACC,SAAS;oCACP,cAAc;oCACd,kBAAkB;oCAClB,oBAAoB;gCACtB;gCACA,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAQP,6LAAC,8IAAA,CAAA,UAAe;oBAAC,OAAO;oBAAK,WAAU;8BACrC,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwD;;;;;;0CAGtE,6LAAC;gCAAE,WAAU;0CAA0D;;;;;;0CAIvE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA5NwB;KAAA", "debugId": null}}, {"offset": {"line": 2223, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/BoxSync/2.%20Univeristy/5.%20Lincoln%20University/3.%20MAC/4.%20Portfolio/portfolio_webapp/portfolio/src/data/experience.ts"], "sourcesContent": ["export interface ExperienceItem {\n  id: string\n  title: string\n  organization: string\n  location: string\n  startDate: string\n  endDate: string | 'Present'\n  type: 'education' | 'work' | 'internship' | 'certification' | 'project' | 'volunteer'\n  description: string\n  achievements?: string[]\n  technologies?: string[]\n  grade?: string\n  coursework?: string[]\n  skills?: string[]\n  logo?: string\n  website?: string\n  featured: boolean\n}\n\nexport const experienceData: ExperienceItem[] = [\n  {\n    id: 'lincoln-university-mac',\n    title: 'Master of Applied Computing',\n    organization: 'Lincoln University',\n    location: 'Canterbury, New Zealand',\n    startDate: '2023-02',\n    endDate: '2024-12',\n    type: 'education',\n    description: 'Comprehensive postgraduate program focusing on practical computing applications, software development, data analytics, and emerging technologies. Specialized in cloud computing, cybersecurity, and full-stack development with emphasis on real-world industry applications.',\n    achievements: [\n      'Graduated with Distinction (GPA: 8.5/9.0)',\n      'Dean\\'s List for Academic Excellence (2023, 2024)',\n      'Best Project Award for Smart Farm Management System',\n      'Research Assistant for Agricultural Technology Lab',\n      'President of Computing Students Association'\n    ],\n    coursework: [\n      'Advanced Software Engineering',\n      'Cloud Computing & DevOps',\n      'Data Analytics & Machine Learning',\n      'Cybersecurity & Network Security',\n      'Mobile Application Development',\n      'Database Systems & Big Data',\n      'Human-Computer Interaction',\n      'Project Management & Agile Methods'\n    ],\n    technologies: ['Python', 'Java', 'JavaScript', 'React', 'Node.js', 'AWS', 'Docker', 'PostgreSQL', 'MongoDB', 'TensorFlow'],\n    skills: ['Software Development', 'Data Analysis', 'Cloud Architecture', 'Project Management', 'Research', 'Technical Writing'],\n    website: 'https://www.lincoln.ac.nz',\n    featured: true\n  },\n  {\n    id: 'tech-internship',\n    title: 'Software Development Intern',\n    organization: 'TechStart NZ',\n    location: 'Christchurch, New Zealand',\n    startDate: '2024-06',\n    endDate: '2024-08',\n    type: 'internship',\n    description: 'Summer internship at a growing tech startup focused on agricultural technology solutions. Worked on developing web applications for farm management and contributed to mobile app development for livestock monitoring.',\n    achievements: [\n      'Developed 3 key features for the main farm management platform',\n      'Improved application performance by 40% through code optimization',\n      'Led a team of 2 junior interns on mobile app development',\n      'Presented final project to company stakeholders and investors',\n      'Received offer for full-time position upon graduation'\n    ],\n    technologies: ['React', 'TypeScript', 'Node.js', 'Express', 'PostgreSQL', 'AWS', 'React Native', 'Git'],\n    skills: ['Full-Stack Development', 'Team Leadership', 'Agile Development', 'Code Review', 'Client Communication'],\n    featured: true\n  },\n  {\n    id: 'aws-cloud-practitioner',\n    title: 'AWS Certified Cloud Practitioner',\n    organization: 'Amazon Web Services',\n    location: 'Online',\n    startDate: '2024-03',\n    endDate: '2024-03',\n    type: 'certification',\n    description: 'Foundational certification demonstrating understanding of AWS cloud concepts, services, security, architecture, pricing, and support. Validates ability to define what the AWS Cloud is and the basic global infrastructure.',\n    achievements: [\n      'Passed with score of 890/1000 (85%)',\n      'Completed 40+ hours of study and hands-on labs',\n      'Demonstrated knowledge of core AWS services',\n      'Understanding of cloud economics and billing'\n    ],\n    technologies: ['AWS', 'EC2', 'S3', 'RDS', 'Lambda', 'CloudFormation', 'IAM', 'VPC'],\n    skills: ['Cloud Computing', 'AWS Services', 'Cloud Security', 'Cost Optimization'],\n    website: 'https://aws.amazon.com/certification/',\n    featured: true\n  },\n  {\n    id: 'bachelor-computer-science',\n    title: 'Bachelor of Computer Science',\n    organization: 'University of Canterbury',\n    location: 'Christchurch, New Zealand',\n    startDate: '2020-02',\n    endDate: '2022-12',\n    type: 'education',\n    description: 'Comprehensive undergraduate program covering fundamental computer science concepts, programming, algorithms, data structures, and software engineering principles. Strong foundation in mathematics and computational thinking.',\n    grade: 'First Class Honours (GPA: 8.2/9.0)',\n    achievements: [\n      'First Class Honours with Distinction',\n      'Computer Science Prize for Outstanding Academic Achievement',\n      'Tutor for COSC122 (Introduction to Computer Science)',\n      'Member of Programming Competition Team (ACM ICPC)',\n      'Volunteer at Hour of Code events for local schools'\n    ],\n    coursework: [\n      'Data Structures & Algorithms',\n      'Software Engineering',\n      'Database Systems',\n      'Computer Networks',\n      'Operating Systems',\n      'Artificial Intelligence',\n      'Computer Graphics',\n      'Discrete Mathematics'\n    ],\n    technologies: ['Java', 'Python', 'C++', 'SQL', 'HTML/CSS', 'JavaScript', 'Git', 'Linux'],\n    skills: ['Programming', 'Algorithm Design', 'Problem Solving', 'Mathematical Analysis', 'Technical Communication'],\n    website: 'https://www.canterbury.ac.nz',\n    featured: false\n  },\n  {\n    id: 'microsoft-azure-fundamentals',\n    title: 'Microsoft Azure Fundamentals (AZ-900)',\n    organization: 'Microsoft',\n    location: 'Online',\n    startDate: '2024-01',\n    endDate: '2024-01',\n    type: 'certification',\n    description: 'Foundational certification covering Azure cloud concepts, core Azure services, security, privacy, compliance, trust, and Azure pricing and support.',\n    achievements: [\n      'Passed with score of 850/1000',\n      'Demonstrated understanding of cloud concepts',\n      'Knowledge of Azure core services and solutions',\n      'Understanding of Azure security and compliance'\n    ],\n    technologies: ['Microsoft Azure', 'Azure Active Directory', 'Azure Storage', 'Azure Compute', 'Azure Networking'],\n    skills: ['Cloud Computing', 'Azure Services', 'Cloud Security', 'Identity Management'],\n    website: 'https://docs.microsoft.com/en-us/learn/certifications/azure-fundamentals/',\n    featured: false\n  },\n  {\n    id: 'part-time-developer',\n    title: 'Part-time Web Developer',\n    organization: 'Local Business Solutions',\n    location: 'Christchurch, New Zealand',\n    startDate: '2023-03',\n    endDate: '2024-02',\n    type: 'work',\n    description: 'Part-time role developing websites and web applications for local small businesses while completing Master\\'s degree. Focused on creating responsive, user-friendly websites that help businesses establish their online presence.',\n    achievements: [\n      'Developed 8+ websites for local businesses',\n      'Improved client website loading speeds by average of 60%',\n      'Implemented SEO best practices resulting in 40% increase in organic traffic',\n      'Managed client relationships and project timelines independently',\n      'Trained 2 junior developers in modern web development practices'\n    ],\n    technologies: ['React', 'Next.js', 'WordPress', 'PHP', 'MySQL', 'HTML/CSS', 'JavaScript', 'Figma'],\n    skills: ['Web Development', 'Client Management', 'SEO Optimization', 'Responsive Design', 'Project Management'],\n    featured: false\n  },\n  {\n    id: 'google-analytics-certified',\n    title: 'Google Analytics Certified',\n    organization: 'Google',\n    location: 'Online',\n    startDate: '2023-11',\n    endDate: '2023-11',\n    type: 'certification',\n    description: 'Certification demonstrating proficiency in Google Analytics, including how to set up and configure Analytics accounts, implement tracking code, analyze reports, and set up goals and campaign tracking.',\n    achievements: [\n      'Passed Google Analytics Individual Qualification (IQ)',\n      'Demonstrated expertise in web analytics',\n      'Understanding of conversion tracking and attribution',\n      'Knowledge of audience analysis and reporting'\n    ],\n    technologies: ['Google Analytics', 'Google Tag Manager', 'Google Ads', 'Data Studio'],\n    skills: ['Web Analytics', 'Data Analysis', 'Digital Marketing', 'Conversion Optimization'],\n    website: 'https://skillshop.exceedlms.com/student/catalog/list?category_ids=53-google-analytics-4',\n    featured: false\n  },\n  {\n    id: 'volunteer-coding-instructor',\n    title: 'Volunteer Coding Instructor',\n    organization: 'Code Club Aotearoa',\n    location: 'Christchurch, New Zealand',\n    startDate: '2022-06',\n    endDate: 'Present',\n    type: 'volunteer',\n    description: 'Volunteer instructor teaching programming fundamentals to children aged 9-13 at local schools and community centers. Focus on making coding accessible and fun through interactive projects and games.',\n    achievements: [\n      'Taught 50+ children basic programming concepts',\n      'Developed curriculum for Scratch and Python workshops',\n      'Organized coding competitions and showcase events',\n      'Mentored 5 students who went on to pursue computer science',\n      'Received Community Volunteer Award 2023'\n    ],\n    technologies: ['Scratch', 'Python', 'HTML/CSS', 'JavaScript', 'Raspberry Pi'],\n    skills: ['Teaching', 'Curriculum Development', 'Public Speaking', 'Mentoring', 'Community Engagement'],\n    website: 'https://codeclub.nz',\n    featured: false\n  }\n]\n\nexport const experienceTypes = [\n  { id: 'all', name: 'All Experience', icon: '📋', color: 'bg-gray-500' },\n  { id: 'education', name: 'Education', icon: '🎓', color: 'bg-blue-500' },\n  { id: 'work', name: 'Work Experience', icon: '💼', color: 'bg-green-500' },\n  { id: 'internship', name: 'Internships', icon: '🚀', color: 'bg-purple-500' },\n  { id: 'certification', name: 'Certifications', icon: '🏆', color: 'bg-yellow-500' },\n  { id: 'volunteer', name: 'Volunteer Work', icon: '❤️', color: 'bg-red-500' },\n]\n\nexport const getExperienceIcon = (type: string): string => {\n  const typeMap: { [key: string]: string } = {\n    education: '🎓',\n    work: '💼',\n    internship: '🚀',\n    certification: '🏆',\n    project: '💻',\n    volunteer: '❤️'\n  }\n  return typeMap[type] || '📋'\n}\n\nexport const getExperienceColor = (type: string): string => {\n  const colorMap: { [key: string]: string } = {\n    education: 'bg-blue-500',\n    work: 'bg-green-500',\n    internship: 'bg-purple-500',\n    certification: 'bg-yellow-500',\n    project: 'bg-indigo-500',\n    volunteer: 'bg-red-500'\n  }\n  return colorMap[type] || 'bg-gray-500'\n}\n"], "names": [], "mappings": ";;;;;;AAmBO,MAAM,iBAAmC;IAC9C;QACE,IAAI;QACJ,OAAO;QACP,cAAc;QACd,UAAU;QACV,WAAW;QACX,SAAS;QACT,MAAM;QACN,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;YACV;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAU;YAAQ;YAAc;YAAS;YAAW;YAAO;YAAU;YAAc;YAAW;SAAa;QAC1H,QAAQ;YAAC;YAAwB;YAAiB;YAAsB;YAAsB;YAAY;SAAoB;QAC9H,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,cAAc;QACd,UAAU;QACV,WAAW;QACX,SAAS;QACT,MAAM;QACN,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAS;YAAc;YAAW;YAAW;YAAc;YAAO;YAAgB;SAAM;QACvG,QAAQ;YAAC;YAA0B;YAAmB;YAAqB;YAAe;SAAuB;QACjH,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,cAAc;QACd,UAAU;QACV,WAAW;QACX,SAAS;QACT,MAAM;QACN,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAO;YAAO;YAAM;YAAO;YAAU;YAAkB;YAAO;SAAM;QACnF,QAAQ;YAAC;YAAmB;YAAgB;YAAkB;SAAoB;QAClF,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,cAAc;QACd,UAAU;QACV,WAAW;QACX,SAAS;QACT,MAAM;QACN,aAAa;QACb,OAAO;QACP,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;YACV;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAQ;YAAU;YAAO;YAAO;YAAY;YAAc;YAAO;SAAQ;QACxF,QAAQ;YAAC;YAAe;YAAoB;YAAmB;YAAyB;SAA0B;QAClH,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,cAAc;QACd,UAAU;QACV,WAAW;QACX,SAAS;QACT,MAAM;QACN,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAmB;YAA0B;YAAiB;YAAiB;SAAmB;QACjH,QAAQ;YAAC;YAAmB;YAAkB;YAAkB;SAAsB;QACtF,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,cAAc;QACd,UAAU;QACV,WAAW;QACX,SAAS;QACT,MAAM;QACN,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAS;YAAW;YAAa;YAAO;YAAS;YAAY;YAAc;SAAQ;QAClG,QAAQ;YAAC;YAAmB;YAAqB;YAAoB;YAAqB;SAAqB;QAC/G,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,cAAc;QACd,UAAU;QACV,WAAW;QACX,SAAS;QACT,MAAM;QACN,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAoB;YAAsB;YAAc;SAAc;QACrF,QAAQ;YAAC;YAAiB;YAAiB;YAAqB;SAA0B;QAC1F,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,cAAc;QACd,UAAU;QACV,WAAW;QACX,SAAS;QACT,MAAM;QACN,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAW;YAAU;YAAY;YAAc;SAAe;QAC7E,QAAQ;YAAC;YAAY;YAA0B;YAAmB;YAAa;SAAuB;QACtG,SAAS;QACT,UAAU;IACZ;CACD;AAEM,MAAM,kBAAkB;IAC7B;QAAE,IAAI;QAAO,MAAM;QAAkB,MAAM;QAAM,OAAO;IAAc;IACtE;QAAE,IAAI;QAAa,MAAM;QAAa,MAAM;QAAM,OAAO;IAAc;IACvE;QAAE,IAAI;QAAQ,MAAM;QAAmB,MAAM;QAAM,OAAO;IAAe;IACzE;QAAE,IAAI;QAAc,MAAM;QAAe,MAAM;QAAM,OAAO;IAAgB;IAC5E;QAAE,IAAI;QAAiB,MAAM;QAAkB,MAAM;QAAM,OAAO;IAAgB;IAClF;QAAE,IAAI;QAAa,MAAM;QAAkB,MAAM;QAAM,OAAO;IAAa;CAC5E;AAEM,MAAM,oBAAoB,CAAC;IAChC,MAAM,UAAqC;QACzC,WAAW;QACX,MAAM;QACN,YAAY;QACZ,eAAe;QACf,SAAS;QACT,WAAW;IACb;IACA,OAAO,OAAO,CAAC,KAAK,IAAI;AAC1B;AAEO,MAAM,qBAAqB,CAAC;IACjC,MAAM,WAAsC;QAC1C,WAAW;QACX,MAAM;QACN,YAAY;QACZ,eAAe;QACf,SAAS;QACT,WAAW;IACb;IACA,OAAO,QAAQ,CAAC,KAAK,IAAI;AAC3B", "debugId": null}}, {"offset": {"line": 2594, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/BoxSync/2.%20Univeristy/5.%20Lincoln%20University/3.%20MAC/4.%20Portfolio/portfolio_webapp/portfolio/src/components/ui/TimelineItem.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { ExternalLink, Calendar, MapPin, Award, ChevronDown, ChevronUp } from 'lucide-react';\nimport { ExperienceItem, getExperienceIcon, getExperienceColor } from '@/data/experience';\nimport { getTechnologyColor } from '@/data/projects';\nimport AnimatedSection from './AnimatedSection';\n\ninterface TimelineItemProps {\n  experience: ExperienceItem;\n  delay?: number;\n  isLast?: boolean;\n}\n\nexport default function TimelineItem({ experience, delay = 0, isLast = false }: TimelineItemProps) {\n  const [isExpanded, setIsExpanded] = useState(false);\n\n  const formatDate = (dateString: string) => {\n    if (dateString === 'Present') return 'Present';\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-NZ', { \n      year: 'numeric', \n      month: 'short' \n    });\n  };\n\n  const calculateDuration = (startDate: string, endDate: string) => {\n    const start = new Date(startDate);\n    const end = endDate === 'Present' ? new Date() : new Date(endDate);\n    const diffTime = Math.abs(end.getTime() - start.getTime());\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    const diffMonths = Math.floor(diffDays / 30);\n    const diffYears = Math.floor(diffMonths / 12);\n    \n    if (diffYears > 0) {\n      const remainingMonths = diffMonths % 12;\n      return `${diffYears} year${diffYears > 1 ? 's' : ''}${remainingMonths > 0 ? ` ${remainingMonths} month${remainingMonths > 1 ? 's' : ''}` : ''}`;\n    } else if (diffMonths > 0) {\n      return `${diffMonths} month${diffMonths > 1 ? 's' : ''}`;\n    } else {\n      return 'Less than a month';\n    }\n  };\n\n  const getTypeLabel = (type: string) => {\n    const labels: { [key: string]: string } = {\n      education: 'Education',\n      work: 'Work Experience',\n      internship: 'Internship',\n      certification: 'Certification',\n      project: 'Project',\n      volunteer: 'Volunteer Work'\n    };\n    return labels[type] || type;\n  };\n\n  return (\n    <AnimatedSection delay={delay}>\n      <div className=\"relative flex items-start space-x-4 pb-8\">\n        {/* Timeline Line */}\n        {!isLast && (\n          <div className=\"absolute left-6 top-12 w-0.5 h-full bg-gray-200 dark:bg-gray-700\"></div>\n        )}\n        \n        {/* Timeline Icon */}\n        <div className={`flex-shrink-0 w-12 h-12 rounded-full ${getExperienceColor(experience.type)} flex items-center justify-center text-white text-xl shadow-lg z-10`}>\n          <span>{getExperienceIcon(experience.type)}</span>\n        </div>\n\n        {/* Content */}\n        <div className=\"flex-1 min-w-0\">\n          <div className=\"bg-white dark:bg-gray-900 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden\">\n            {/* Header */}\n            <div className=\"p-6 border-b border-gray-100 dark:border-gray-700\">\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex-1\">\n                  {/* Type Badge */}\n                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getExperienceColor(experience.type)} text-white mb-2`}>\n                    {getTypeLabel(experience.type)}\n                  </span>\n                  \n                  {/* Title and Organization */}\n                  <h3 className=\"text-xl font-bold text-gray-900 dark:text-white mb-1\">\n                    {experience.title}\n                  </h3>\n                  <div className=\"flex items-center space-x-2 text-gray-600 dark:text-gray-300 mb-2\">\n                    <span className=\"font-medium\">{experience.organization}</span>\n                    {experience.website && (\n                      <a\n                        href={experience.website}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300\"\n                      >\n                        <ExternalLink className=\"w-4 h-4\" />\n                      </a>\n                    )}\n                  </div>\n                  \n                  {/* Date and Location */}\n                  <div className=\"flex flex-col sm:flex-row sm:items-center sm:space-x-4 text-sm text-gray-500 dark:text-gray-400\">\n                    <div className=\"flex items-center space-x-1\">\n                      <Calendar className=\"w-4 h-4\" />\n                      <span>\n                        {formatDate(experience.startDate)} - {formatDate(experience.endDate)}\n                      </span>\n                      <span className=\"text-gray-400\">\n                        ({calculateDuration(experience.startDate, experience.endDate)})\n                      </span>\n                    </div>\n                    <div className=\"flex items-center space-x-1 mt-1 sm:mt-0\">\n                      <MapPin className=\"w-4 h-4\" />\n                      <span>{experience.location}</span>\n                    </div>\n                  </div>\n\n                  {/* Grade (for education) */}\n                  {experience.grade && (\n                    <div className=\"mt-2 flex items-center space-x-1 text-sm\">\n                      <Award className=\"w-4 h-4 text-yellow-500\" />\n                      <span className=\"font-medium text-gray-700 dark:text-gray-300\">\n                        {experience.grade}\n                      </span>\n                    </div>\n                  )}\n                </div>\n\n                {/* Featured Badge */}\n                {experience.featured && (\n                  <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200\">\n                    ⭐ Featured\n                  </span>\n                )}\n              </div>\n\n              {/* Description */}\n              <p className=\"text-gray-600 dark:text-gray-300 mt-4 leading-relaxed\">\n                {experience.description}\n              </p>\n\n              {/* Technologies */}\n              {experience.technologies && experience.technologies.length > 0 && (\n                <div className=\"mt-4\">\n                  <h4 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Technologies Used:\n                  </h4>\n                  <div className=\"flex flex-wrap gap-1\">\n                    {experience.technologies.slice(0, 6).map((tech) => (\n                      <span\n                        key={tech}\n                        className={`px-2 py-1 rounded-full text-xs font-medium ${getTechnologyColor(tech)}`}\n                      >\n                        {tech}\n                      </span>\n                    ))}\n                    {experience.technologies.length > 6 && (\n                      <span className=\"px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300\">\n                        +{experience.technologies.length - 6} more\n                      </span>\n                    )}\n                  </div>\n                </div>\n              )}\n\n              {/* Expand Button */}\n              {(experience.achievements || experience.coursework || experience.skills) && (\n                <button\n                  onClick={() => setIsExpanded(!isExpanded)}\n                  className=\"mt-4 flex items-center space-x-1 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm font-medium transition-colors\"\n                >\n                  <span>{isExpanded ? 'Show Less' : 'Show More Details'}</span>\n                  {isExpanded ? (\n                    <ChevronUp className=\"w-4 h-4\" />\n                  ) : (\n                    <ChevronDown className=\"w-4 h-4\" />\n                  )}\n                </button>\n              )}\n            </div>\n\n            {/* Expanded Content */}\n            {isExpanded && (\n              <div className=\"p-6 bg-gray-50 dark:bg-gray-800 space-y-6\">\n                {/* Achievements */}\n                {experience.achievements && experience.achievements.length > 0 && (\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900 dark:text-white mb-3 flex items-center\">\n                      <Award className=\"w-4 h-4 mr-2 text-yellow-500\" />\n                      Key Achievements\n                    </h4>\n                    <ul className=\"space-y-2\">\n                      {experience.achievements.map((achievement, index) => (\n                        <li key={index} className=\"flex items-start space-x-2 text-sm text-gray-600 dark:text-gray-300\">\n                          <span className=\"text-green-500 mt-1\">•</span>\n                          <span>{achievement}</span>\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n\n                {/* Coursework */}\n                {experience.coursework && experience.coursework.length > 0 && (\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900 dark:text-white mb-3\">\n                      Key Coursework\n                    </h4>\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2\">\n                      {experience.coursework.map((course, index) => (\n                        <div key={index} className=\"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300\">\n                          <span className=\"text-blue-500\">•</span>\n                          <span>{course}</span>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                )}\n\n                {/* Skills Developed */}\n                {experience.skills && experience.skills.length > 0 && (\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900 dark:text-white mb-3\">\n                      Skills Developed\n                    </h4>\n                    <div className=\"flex flex-wrap gap-2\">\n                      {experience.skills.map((skill) => (\n                        <span\n                          key={skill}\n                          className=\"px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm font-medium\"\n                        >\n                          {skill}\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n                )}\n\n                {/* All Technologies (if expanded) */}\n                {experience.technologies && experience.technologies.length > 6 && (\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900 dark:text-white mb-3\">\n                      All Technologies Used\n                    </h4>\n                    <div className=\"flex flex-wrap gap-1\">\n                      {experience.technologies.map((tech) => (\n                        <span\n                          key={tech}\n                          className={`px-2 py-1 rounded-full text-xs font-medium ${getTechnologyColor(tech)}`}\n                        >\n                          {tech}\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </AnimatedSection>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAce,SAAS,aAAa,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,SAAS,KAAK,EAAqB;;IAC/F,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,aAAa,CAAC;QAClB,IAAI,eAAe,WAAW,OAAO;QACrC,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;QACT;IACF;IAEA,MAAM,oBAAoB,CAAC,WAAmB;QAC5C,MAAM,QAAQ,IAAI,KAAK;QACvB,MAAM,MAAM,YAAY,YAAY,IAAI,SAAS,IAAI,KAAK;QAC1D,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,OAAO,KAAK,MAAM,OAAO;QACvD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAC1D,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW;QACzC,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa;QAE1C,IAAI,YAAY,GAAG;YACjB,MAAM,kBAAkB,aAAa;YACrC,OAAO,GAAG,UAAU,KAAK,EAAE,YAAY,IAAI,MAAM,KAAK,kBAAkB,IAAI,CAAC,CAAC,EAAE,gBAAgB,MAAM,EAAE,kBAAkB,IAAI,MAAM,IAAI,GAAG,IAAI;QACjJ,OAAO,IAAI,aAAa,GAAG;YACzB,OAAO,GAAG,WAAW,MAAM,EAAE,aAAa,IAAI,MAAM,IAAI;QAC1D,OAAO;YACL,OAAO;QACT;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAoC;YACxC,WAAW;YACX,MAAM;YACN,YAAY;YACZ,eAAe;YACf,SAAS;YACT,WAAW;QACb;QACA,OAAO,MAAM,CAAC,KAAK,IAAI;IACzB;IAEA,qBACE,6LAAC,8IAAA,CAAA,UAAe;QAAC,OAAO;kBACtB,cAAA,6LAAC;YAAI,WAAU;;gBAEZ,CAAC,wBACA,6LAAC;oBAAI,WAAU;;;;;;8BAIjB,6LAAC;oBAAI,WAAW,CAAC,qCAAqC,EAAE,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW,IAAI,EAAE,mEAAmE,CAAC;8BAC9J,cAAA,6LAAC;kCAAM,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW,IAAI;;;;;;;;;;;8BAI1C,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAK,WAAW,CAAC,oEAAoE,EAAE,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW,IAAI,EAAE,gBAAgB,CAAC;kEAC1I,aAAa,WAAW,IAAI;;;;;;kEAI/B,6LAAC;wDAAG,WAAU;kEACX,WAAW,KAAK;;;;;;kEAEnB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAe,WAAW,YAAY;;;;;;4DACrD,WAAW,OAAO,kBACjB,6LAAC;gEACC,MAAM,WAAW,OAAO;gEACxB,QAAO;gEACP,KAAI;gEACJ,WAAU;0EAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;;;;;;;kEAM9B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,6LAAC;;4EACE,WAAW,WAAW,SAAS;4EAAE;4EAAI,WAAW,WAAW,OAAO;;;;;;;kFAErE,6LAAC;wEAAK,WAAU;;4EAAgB;4EAC5B,kBAAkB,WAAW,SAAS,EAAE,WAAW,OAAO;4EAAE;;;;;;;;;;;;;0EAGlE,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,6LAAC;kFAAM,WAAW,QAAQ;;;;;;;;;;;;;;;;;;oDAK7B,WAAW,KAAK,kBACf,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;gEAAK,WAAU;0EACb,WAAW,KAAK;;;;;;;;;;;;;;;;;;4CAOxB,WAAW,QAAQ,kBAClB,6LAAC;gDAAK,WAAU;0DAA4I;;;;;;;;;;;;kDAOhK,6LAAC;wCAAE,WAAU;kDACV,WAAW,WAAW;;;;;;oCAIxB,WAAW,YAAY,IAAI,WAAW,YAAY,CAAC,MAAM,GAAG,mBAC3D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,6LAAC;gDAAI,WAAU;;oDACZ,WAAW,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACxC,6LAAC;4DAEC,WAAW,CAAC,2CAA2C,EAAE,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;sEAElF;2DAHI;;;;;oDAMR,WAAW,YAAY,CAAC,MAAM,GAAG,mBAChC,6LAAC;wDAAK,WAAU;;4DAA2G;4DACvH,WAAW,YAAY,CAAC,MAAM,GAAG;4DAAE;;;;;;;;;;;;;;;;;;;oCAQ9C,CAAC,WAAW,YAAY,IAAI,WAAW,UAAU,IAAI,WAAW,MAAM,mBACrE,6LAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;;0DAEV,6LAAC;0DAAM,aAAa,cAAc;;;;;;4CACjC,2BACC,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;qEAErB,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;;4BAO9B,4BACC,6LAAC;gCAAI,WAAU;;oCAEZ,WAAW,YAAY,IAAI,WAAW,YAAY,CAAC,MAAM,GAAG,mBAC3D,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiC;;;;;;;0DAGpD,6LAAC;gDAAG,WAAU;0DACX,WAAW,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,sBACzC,6LAAC;wDAAe,WAAU;;0EACxB,6LAAC;gEAAK,WAAU;0EAAsB;;;;;;0EACtC,6LAAC;0EAAM;;;;;;;uDAFA;;;;;;;;;;;;;;;;oCAUhB,WAAW,UAAU,IAAI,WAAW,UAAU,CAAC,MAAM,GAAG,mBACvD,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;0DAGjE,6LAAC;gDAAI,WAAU;0DACZ,WAAW,UAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAClC,6LAAC;wDAAgB,WAAU;;0EACzB,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,6LAAC;0EAAM;;;;;;;uDAFC;;;;;;;;;;;;;;;;oCAUjB,WAAW,MAAM,IAAI,WAAW,MAAM,CAAC,MAAM,GAAG,mBAC/C,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;0DAGjE,6LAAC;gDAAI,WAAU;0DACZ,WAAW,MAAM,CAAC,GAAG,CAAC,CAAC,sBACtB,6LAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;;;;;;;oCAWd,WAAW,YAAY,IAAI,WAAW,YAAY,CAAC,MAAM,GAAG,mBAC3D,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;0DAGjE,6LAAC;gDAAI,WAAU;0DACZ,WAAW,YAAY,CAAC,GAAG,CAAC,CAAC,qBAC5B,6LAAC;wDAEC,WAAW,CAAC,2CAA2C,EAAE,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;kEAElF;uDAHI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgB/B;GAxPwB;KAAA", "debugId": null}}, {"offset": {"line": 3159, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/BoxSync/2.%20Univeristy/5.%20Lincoln%20University/3.%20MAC/4.%20Portfolio/portfolio_webapp/portfolio/src/components/sections/ExperienceSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useMemo } from 'react';\nimport { experienceData, experienceTypes, ExperienceItem } from '@/data/experience';\nimport TimelineItem from '@/components/ui/TimelineItem';\nimport AnimatedSection from '@/components/ui/AnimatedSection';\nimport { GraduationCap, Briefcase, Award, Heart, Calendar, TrendingUp } from 'lucide-react';\n\nexport default function ExperienceSection() {\n  const [activeFilter, setActiveFilter] = useState('all');\n  const [sortOrder, setSortOrder] = useState<'newest' | 'oldest'>('newest');\n\n  // Filter and sort experience data\n  const filteredAndSortedExperience = useMemo(() => {\n    let filtered = experienceData;\n\n    // Filter by type\n    if (activeFilter !== 'all') {\n      filtered = filtered.filter(item => item.type === activeFilter);\n    }\n\n    // Sort by date\n    const sorted = [...filtered].sort((a, b) => {\n      const dateA = new Date(a.startDate);\n      const dateB = new Date(b.startDate);\n      \n      if (sortOrder === 'newest') {\n        return dateB.getTime() - dateA.getTime();\n      } else {\n        return dateA.getTime() - dateB.getTime();\n      }\n    });\n\n    return sorted;\n  }, [activeFilter, sortOrder]);\n\n  // Calculate statistics\n  const stats = useMemo(() => {\n    const education = experienceData.filter(item => item.type === 'education').length;\n    const work = experienceData.filter(item => item.type === 'work' || item.type === 'internship').length;\n    const certifications = experienceData.filter(item => item.type === 'certification').length;\n    const volunteer = experienceData.filter(item => item.type === 'volunteer').length;\n\n    return { education, work, certifications, volunteer };\n  }, []);\n\n  const getFilterIcon = (filterId: string) => {\n    const iconMap: { [key: string]: React.ReactNode } = {\n      all: <Calendar className=\"w-4 h-4\" />,\n      education: <GraduationCap className=\"w-4 h-4\" />,\n      work: <Briefcase className=\"w-4 h-4\" />,\n      internship: <TrendingUp className=\"w-4 h-4\" />,\n      certification: <Award className=\"w-4 h-4\" />,\n      volunteer: <Heart className=\"w-4 h-4\" />\n    };\n    return iconMap[filterId] || <Calendar className=\"w-4 h-4\" />;\n  };\n\n  return (\n    <section id=\"experience\" className=\"py-20 bg-gray-50 dark:bg-gray-800\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <AnimatedSection className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n            Experience & Education\n          </h2>\n          <p className=\"text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8\">\n            My academic journey and professional experience in software development, \n            from foundational education to hands-on industry experience and continuous learning.\n          </p>\n\n          {/* Statistics */}\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-6 max-w-3xl mx-auto\">\n            <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4\">\n              <div className=\"flex items-center justify-center space-x-2 text-blue-600 dark:text-blue-400\">\n                <GraduationCap className=\"w-5 h-5\" />\n                <span className=\"text-2xl font-bold\">{stats.education}</span>\n              </div>\n              <p className=\"text-sm text-blue-700 dark:text-blue-300 mt-1\">Degrees</p>\n            </div>\n            \n            <div className=\"bg-green-50 dark:bg-green-900/20 rounded-lg p-4\">\n              <div className=\"flex items-center justify-center space-x-2 text-green-600 dark:text-green-400\">\n                <Briefcase className=\"w-5 h-5\" />\n                <span className=\"text-2xl font-bold\">{stats.work}</span>\n              </div>\n              <p className=\"text-sm text-green-700 dark:text-green-300 mt-1\">Work Experience</p>\n            </div>\n            \n            <div className=\"bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4\">\n              <div className=\"flex items-center justify-center space-x-2 text-yellow-600 dark:text-yellow-400\">\n                <Award className=\"w-5 h-5\" />\n                <span className=\"text-2xl font-bold\">{stats.certifications}</span>\n              </div>\n              <p className=\"text-sm text-yellow-700 dark:text-yellow-300 mt-1\">Certifications</p>\n            </div>\n            \n            <div className=\"bg-red-50 dark:bg-red-900/20 rounded-lg p-4\">\n              <div className=\"flex items-center justify-center space-x-2 text-red-600 dark:text-red-400\">\n                <Heart className=\"w-5 h-5\" />\n                <span className=\"text-2xl font-bold\">{stats.volunteer}</span>\n              </div>\n              <p className=\"text-sm text-red-700 dark:text-red-300 mt-1\">Volunteer Work</p>\n            </div>\n          </div>\n        </AnimatedSection>\n\n        {/* Filters and Controls */}\n        <AnimatedSection delay={200} className=\"mb-12\">\n          <div className=\"bg-white dark:bg-gray-900 rounded-xl p-6 shadow-lg\">\n            <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0\">\n              {/* Type Filters */}\n              <div className=\"flex flex-wrap gap-2\">\n                {experienceTypes.map((type) => (\n                  <button\n                    key={type.id}\n                    onClick={() => setActiveFilter(type.id)}\n                    className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ${\n                      activeFilter === type.id\n                        ? 'bg-blue-600 text-white shadow-lg transform scale-105'\n                        : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-600'\n                    }`}\n                  >\n                    {getFilterIcon(type.id)}\n                    <span>{type.name}</span>\n                  </button>\n                ))}\n              </div>\n\n              {/* Sort Controls */}\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">Sort by:</span>\n                <select\n                  value={sortOrder}\n                  onChange={(e) => setSortOrder(e.target.value as 'newest' | 'oldest')}\n                  className=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                >\n                  <option value=\"newest\">Newest First</option>\n                  <option value=\"oldest\">Oldest First</option>\n                </select>\n              </div>\n            </div>\n\n            {/* Results Summary */}\n            <div className=\"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 text-center\">\n                Showing <span className=\"font-semibold text-blue-600 dark:text-blue-400\">{filteredAndSortedExperience.length}</span> \n                {' '}item{filteredAndSortedExperience.length !== 1 ? 's' : ''}\n                {activeFilter !== 'all' && (\n                  <span> in <span className=\"font-semibold\">{experienceTypes.find(t => t.id === activeFilter)?.name}</span></span>\n                )}\n              </p>\n            </div>\n          </div>\n        </AnimatedSection>\n\n        {/* Timeline */}\n        <AnimatedSection delay={400}>\n          <div className=\"relative\">\n            {filteredAndSortedExperience.length > 0 ? (\n              <div className=\"space-y-0\">\n                {filteredAndSortedExperience.map((experience, index) => (\n                  <TimelineItem\n                    key={experience.id}\n                    experience={experience}\n                    delay={600 + index * 100}\n                    isLast={index === filteredAndSortedExperience.length - 1}\n                  />\n                ))}\n              </div>\n            ) : (\n              <div className=\"text-center py-12\">\n                <div className=\"w-24 h-24 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center\">\n                  <Calendar className=\"w-12 h-12 text-gray-400\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n                  No experience found\n                </h3>\n                <p className=\"text-gray-600 dark:text-gray-300 mb-4\">\n                  Try adjusting your filters to see more results.\n                </p>\n                <button\n                  onClick={() => setActiveFilter('all')}\n                  className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n                >\n                  Show All Experience\n                </button>\n              </div>\n            )}\n          </div>\n        </AnimatedSection>\n\n        {/* Call to Action */}\n        <AnimatedSection delay={800} className=\"mt-16 text-center\">\n          <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-8\">\n            <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n              Ready to Contribute\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto\">\n              With a strong educational foundation and hands-on experience, I'm excited to bring my skills \n              to a dynamic team and continue growing as a software developer.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <a\n                href=\"#contact\"\n                className=\"px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium\"\n              >\n                Get In Touch\n              </a>\n              <a\n                href=\"/resume.pdf\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"px-8 py-3 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-600 hover:text-white transition-colors font-medium\"\n              >\n                Download Resume\n              </a>\n            </div>\n          </div>\n        </AnimatedSection>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAEhE,kCAAkC;IAClC,MAAM,8BAA8B,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;kEAAE;YAC1C,IAAI,WAAW,4HAAA,CAAA,iBAAc;YAE7B,iBAAiB;YACjB,IAAI,iBAAiB,OAAO;gBAC1B,WAAW,SAAS,MAAM;8EAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;;YACnD;YAEA,eAAe;YACf,MAAM,SAAS;mBAAI;aAAS,CAAC,IAAI;iFAAC,CAAC,GAAG;oBACpC,MAAM,QAAQ,IAAI,KAAK,EAAE,SAAS;oBAClC,MAAM,QAAQ,IAAI,KAAK,EAAE,SAAS;oBAElC,IAAI,cAAc,UAAU;wBAC1B,OAAO,MAAM,OAAO,KAAK,MAAM,OAAO;oBACxC,OAAO;wBACL,OAAO,MAAM,OAAO,KAAK,MAAM,OAAO;oBACxC;gBACF;;YAEA,OAAO;QACT;iEAAG;QAAC;QAAc;KAAU;IAE5B,uBAAuB;IACvB,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;4CAAE;YACpB,MAAM,YAAY,4HAAA,CAAA,iBAAc,CAAC,MAAM;oDAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;mDAAa,MAAM;YACjF,MAAM,OAAO,4HAAA,CAAA,iBAAc,CAAC,MAAM;oDAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI,KAAK;mDAAc,MAAM;YACrG,MAAM,iBAAiB,4HAAA,CAAA,iBAAc,CAAC,MAAM;oDAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;mDAAiB,MAAM;YAC1F,MAAM,YAAY,4HAAA,CAAA,iBAAc,CAAC,MAAM;oDAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;mDAAa,MAAM;YAEjF,OAAO;gBAAE;gBAAW;gBAAM;gBAAgB;YAAU;QACtD;2CAAG,EAAE;IAEL,MAAM,gBAAgB,CAAC;QACrB,MAAM,UAA8C;YAClD,mBAAK,6LAAC,6MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YACzB,yBAAW,6LAAC,2NAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;YACpC,oBAAM,6LAAC,+MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,0BAAY,6LAAC,qNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAClC,6BAAe,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YAChC,yBAAW,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QAC9B;QACA,OAAO,OAAO,CAAC,SAAS,kBAAI,6LAAC,6MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAClD;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAa,WAAU;kBACjC,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,8IAAA,CAAA,UAAe;oBAAC,WAAU;;sCACzB,6LAAC;4BAAG,WAAU;sCAAoE;;;;;;sCAGlF,6LAAC;4BAAE,WAAU;sCAAkE;;;;;;sCAM/E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DACzB,6LAAC;oDAAK,WAAU;8DAAsB,MAAM,SAAS;;;;;;;;;;;;sDAEvD,6LAAC;4CAAE,WAAU;sDAAgD;;;;;;;;;;;;8CAG/D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+MAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,6LAAC;oDAAK,WAAU;8DAAsB,MAAM,IAAI;;;;;;;;;;;;sDAElD,6LAAC;4CAAE,WAAU;sDAAkD;;;;;;;;;;;;8CAGjE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAK,WAAU;8DAAsB,MAAM,cAAc;;;;;;;;;;;;sDAE5D,6LAAC;4CAAE,WAAU;sDAAoD;;;;;;;;;;;;8CAGnE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAK,WAAU;8DAAsB,MAAM,SAAS;;;;;;;;;;;;sDAEvD,6LAAC;4CAAE,WAAU;sDAA8C;;;;;;;;;;;;;;;;;;;;;;;;8BAMjE,6LAAC,8IAAA,CAAA,UAAe;oBAAC,OAAO;oBAAK,WAAU;8BACrC,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACZ,4HAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,CAAC,qBACpB,6LAAC;gDAEC,SAAS,IAAM,gBAAgB,KAAK,EAAE;gDACtC,WAAW,CAAC,yFAAyF,EACnG,iBAAiB,KAAK,EAAE,GACpB,yDACA,yGACJ;;oDAED,cAAc,KAAK,EAAE;kEACtB,6LAAC;kEAAM,KAAK,IAAI;;;;;;;+CATX,KAAK,EAAE;;;;;;;;;;kDAelB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;0DAC3D,6LAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gDAC5C,WAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,6LAAC;wDAAO,OAAM;kEAAS;;;;;;;;;;;;;;;;;;;;;;;;0CAM7B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;;wCAAuD;sDAC1D,6LAAC;4CAAK,WAAU;sDAAkD,4BAA4B,MAAM;;;;;;wCAC3G;wCAAI;wCAAK,4BAA4B,MAAM,KAAK,IAAI,MAAM;wCAC1D,iBAAiB,uBAChB,6LAAC;;gDAAK;8DAAI,6LAAC;oDAAK,WAAU;8DAAiB,4HAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQvG,6LAAC,8IAAA,CAAA,UAAe;oBAAC,OAAO;8BACtB,cAAA,6LAAC;wBAAI,WAAU;kCACZ,4BAA4B,MAAM,GAAG,kBACpC,6LAAC;4BAAI,WAAU;sCACZ,4BAA4B,GAAG,CAAC,CAAC,YAAY,sBAC5C,6LAAC,2IAAA,CAAA,UAAY;oCAEX,YAAY;oCACZ,OAAO,MAAM,QAAQ;oCACrB,QAAQ,UAAU,4BAA4B,MAAM,GAAG;mCAHlD,WAAW,EAAE;;;;;;;;;iDAQxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,6LAAC;oCAAG,WAAU;8CAA2D;;;;;;8CAGzE,6LAAC;oCAAE,WAAU;8CAAwC;;;;;;8CAGrD,6LAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;8BAST,6LAAC,8IAAA,CAAA,UAAe;oBAAC,OAAO;oBAAK,WAAU;8BACrC,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwD;;;;;;0CAGtE,6LAAC;gCAAE,WAAU;0CAA0D;;;;;;0CAIvE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAvNwB;KAAA", "debugId": null}}]}