# 🎨 Customization Guide

## 🎯 Quick Customization Checklist

### **Essential Updates (Do First):**

#### **1. Personal Information**
**File:** `src/app/page.tsx`
```typescript
// Line ~45: Update hero section
<h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6 leading-tight">
  Hi, I'm <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent animate-gradient">
    [YOUR ACTUAL NAME] {/* UPDATE THIS */}
  </span>
</h1>

// Line ~50: Update subtitle
<h2 className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-6 font-medium">
  Master of Applied Computing Graduate {/* UPDATE IF DIFFERENT */}
</h2>

// Line ~55: Update description
<p className="text-lg text-gray-500 dark:text-gray-400 mb-8 leading-relaxed">
  Passionate about <strong>cloud technologies</strong>, <strong>data analytics</strong>, 
  <strong> cybersecurity</strong>, and <strong>full-stack development</strong>. 
  Building innovative solutions for New Zealand's tech landscape. {/* CUSTOMIZE THIS */}
</p>
```

#### **2. Contact Information**
**File:** `src/components/ui/ContactInfo.tsx`
```typescript
// Line ~15: Update contact methods
const contactMethods: ContactMethod[] = [
  {
    icon: <Mail className="w-6 h-6" />,
    label: 'Email',
    value: '<EMAIL>', // YOUR EMAIL
    href: 'mailto:<EMAIL>', // YOUR EMAIL
    description: 'Best way to reach me for professional inquiries'
  },
  {
    icon: <Phone className="w-6 h-6" />,
    label: 'Phone',
    value: '+64 21 123 4567', // YOUR PHONE
    href: 'tel:+6421234567', // YOUR PHONE (no spaces)
    description: 'Available for calls during NZ business hours'
  },
  {
    icon: <MapPin className="w-6 h-6" />,
    label: 'Location',
    value: 'Auckland, New Zealand', // YOUR CITY
    href: 'https://maps.google.com/?q=Auckland,New+Zealand', // YOUR CITY
    description: 'Open to opportunities throughout New Zealand'
  }
];

// Line ~40: Update social links
const socialLinks: SocialLink[] = [
  {
    name: 'GitHub',
    icon: <Github className="w-5 h-5" />,
    href: 'https://github.com/your-username', // YOUR GITHUB
    username: '@your-username', // YOUR GITHUB USERNAME
    description: 'View my code and open source contributions'
  },
  {
    name: 'LinkedIn',
    icon: <Linkedin className="w-5 h-5" />,
    href: 'https://linkedin.com/in/your-profile', // YOUR LINKEDIN
    username: '/in/your-profile', // YOUR LINKEDIN
    description: 'Connect with me professionally'
  }
];

// Line ~85: Update resume download
const handleResumeDownload = () => {
  const link = document.createElement('a');
  link.href = '/resume.pdf';
  link.download = 'John_Smith_Resume.pdf'; // YOUR NAME
  link.click();
};
```

#### **3. Site Metadata**
**File:** `src/app/layout.tsx`
```typescript
// Line ~10: Update metadata
export const metadata: Metadata = {
  title: "John Smith - Portfolio | Master of Applied Computing Graduate", // YOUR NAME
  description: "Portfolio of John Smith, a Master of Applied Computing graduate from Lincoln University, New Zealand. Showcasing cloud technologies, data analytics, cybersecurity, and full-stack development projects.", // YOUR DESCRIPTION
  keywords: "John Smith,portfolio,software developer,cloud computing,data analytics,cybersecurity,full-stack development,New Zealand,Lincoln University", // YOUR KEYWORDS
  authors: [{ name: "John Smith" }], // YOUR NAME
  creator: "John Smith", // YOUR NAME
  openGraph: {
    title: "John Smith - Portfolio", // YOUR NAME
    description: "Master of Applied Computing graduate showcasing technical projects and skills",
    url: "https://johnsmith.dev", // YOUR DOMAIN
    siteName: "John Smith Portfolio", // YOUR NAME
    locale: "en_NZ",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "John Smith - Portfolio", // YOUR NAME
    description: "Master of Applied Computing graduate showcasing technical projects and skills",
  },
};
```

### **Project Customization (Do Second):**

#### **4. Update Project Data**
**File:** `src/data/projects.ts`

**Replace sample projects with your actual projects:**
```typescript
export const projects: Project[] = [
  {
    id: 'your-project-1',
    title: 'Your Actual Project Title',
    description: 'Brief description of your real project',
    longDescription: 'Detailed description of what you built, why you built it, and how it works',
    technologies: ['React', 'TypeScript', 'Node.js'], // Technologies you actually used
    category: 'web', // or 'mobile', 'data', 'cloud', 'security', 'automation'
    image: '/images/projects/your-project-1.jpg', // Add your screenshot
    liveUrl: 'https://your-project.vercel.app', // If deployed
    githubUrl: 'https://github.com/yourusername/your-project', // Your repo
    featured: true,
    status: 'completed',
    impact: 'Specific measurable impact or achievement',
    challenges: ['Challenge 1', 'Challenge 2'],
    learnings: ['What you learned', 'Skills developed']
  },
  // Add more of your actual projects...
];
```

#### **5. Update Experience Data**
**File:** `src/data/experience.ts`

**Update with your actual education and experience:**
```typescript
export const experienceData: ExperienceItem[] = [
  {
    id: 'your-masters-degree',
    title: 'Master of Applied Computing', // Or your actual degree
    organization: 'Lincoln University', // Or your actual university
    location: 'Canterbury, New Zealand',
    startDate: '2023-02', // Your actual dates
    endDate: '2024-12',
    type: 'education',
    description: 'Your actual program description and focus areas',
    achievements: [
      'Your actual achievements',
      'Actual GPA or honors',
      'Real projects or recognition'
    ],
    coursework: [
      'Actual courses you took',
      'Relevant subjects'
    ],
    technologies: ['Technologies you learned'],
    grade: 'Your actual grade/GPA',
    featured: true
  },
  // Add your actual work experience, internships, certifications...
];
```

### **Visual Customization (Do Third):**

#### **6. Add Your Project Images**
**Location:** `public/images/projects/`

**Requirements:**
- Format: JPG or PNG
- Size: 400x300px (4:3 aspect ratio)
- File size: Under 500KB each
- Professional screenshots or mockups

**Files to add:**
- `your-project-1.jpg`
- `your-project-2.jpg`
- `your-project-3.jpg`
- etc.

#### **7. Add Your Resume**
**Location:** `public/resume.pdf`

**Requirements:**
- PDF format
- File size: Under 5MB
- Professional formatting
- 2 pages maximum
- Named: `resume.pdf`

#### **8. Update Skills (Optional)**
**File:** `src/data/skills.ts`

**Adjust skill levels based on your actual experience:**
```typescript
// Update proficiency levels (0-100)
{
  name: 'React',
  level: 85, // Your actual skill level
  category: 'frontend'
},
```

### **Advanced Customization (Optional):**

#### **9. Color Scheme**
**File:** `tailwind.config.ts`
```typescript
// Add custom colors
theme: {
  extend: {
    colors: {
      primary: {
        50: '#eff6ff',
        500: '#3b82f6', // Change to your preferred color
        600: '#2563eb',
        700: '#1d4ed8',
      }
    }
  }
}
```

#### **10. Typography**
**File:** `tailwind.config.ts`
```typescript
// Add custom fonts
theme: {
  extend: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'], // Your preferred font
    }
  }
}
```

## 🔧 Testing Your Changes

### **Development Testing:**
```bash
# Start development server
npm run dev

# Open http://localhost:3000
# Test all sections and functionality
```

### **Build Testing:**
```bash
# Test production build
npm run build
npm run start

# Check for any build errors
```

### **Content Checklist:**
- [ ] All personal information updated
- [ ] Contact methods work correctly
- [ ] Social media links open properly
- [ ] Resume downloads successfully
- [ ] Project images display correctly
- [ ] No placeholder text remains
- [ ] All links are functional

## 🚨 Common Issues & Fixes

### **Images Not Displaying:**
```typescript
// Make sure image paths start with /
image: '/images/projects/your-project.jpg' // Correct
image: 'images/projects/your-project.jpg'  // Wrong
```

### **Links Not Working:**
```typescript
// Email links need mailto:
href: 'mailto:<EMAIL>' // Correct
href: '<EMAIL>'        // Wrong

// Phone links need tel:
href: 'tel:+6421234567' // Correct (no spaces)
href: '+64 21 234 567'  // Wrong
```

### **Build Errors:**
```bash
# Check for TypeScript errors
npx tsc --noEmit

# Check for missing imports
npm run build
```

## 📱 Mobile Testing

**Test on different screen sizes:**
- Mobile: 375px width
- Tablet: 768px width  
- Desktop: 1024px+ width

**Key areas to check:**
- Navigation menu works on mobile
- Contact form is usable
- Text is readable
- Buttons are touch-friendly
- Images scale properly

## 🎯 Final Review

Before deploying, ensure:
- [ ] All placeholder content replaced
- [ ] Personal information is accurate
- [ ] Contact methods are working
- [ ] Resume is current and professional
- [ ] Project descriptions are compelling
- [ ] No broken links or images
- [ ] Site works on mobile devices
- [ ] Loading speed is acceptable

**Your customized portfolio is ready for the job market! 🚀**
