# Getting Started

## 🎯 What We're Building

We're creating a professional portfolio website that showcases your skills, projects, and experience. This website will be:

- **Fast and Modern**: Built with the latest web technologies
- **Responsive**: Works perfectly on phones, tablets, and computers
- **Professional**: Designed to impress potential employers
- **Interactive**: Engaging animations and user interactions
- **SEO-Friendly**: Optimized for search engines

## 📋 Prerequisites

Before we start, you'll need:

### Required Software

1. **Node.js** (version 18 or higher)
   - Download from: https://nodejs.org/
   - This includes npm (Node Package Manager)
   - Think of Node.js as the engine that runs JavaScript on your computer

2. **A Code Editor**
   - **Recommended**: Visual Studio Code (VS Code)
   - Download from: https://code.visualstudio.com/
   - Free, powerful, and has great support for our technologies

3. **A Web Browser**
   - Chrome, Firefox, Safari, or Edge
   - Chrome is recommended for development tools

### Optional but Helpful

1. **Git** for version control
   - Download from: https://git-scm.com/
   - Helps track changes to your code

2. **VS Code Extensions** (install these in VS Code):
   - TypeScript and JavaScript Language Features (built-in)
   - Tailwind CSS IntelliSense
   - ES7+ React/Redux/React-Native snippets
   - Auto Rename Tag
   - Prettier - Code formatter

## 🔧 Installation Steps

### Step 1: Verify Node.js Installation

Open your terminal (Command Prompt on Windows, Terminal on Mac/Linux) and run:

```bash
node --version
npm --version
```

You should see version numbers like:
```
v18.17.0
9.6.7
```

If you get an error, Node.js isn't installed correctly.

### Step 2: Understanding the Project Structure

Our project is organized like this:

```
portfolio/
├── src/                    # Source code
│   ├── app/               # Next.js app directory
│   ├── components/        # Reusable UI components
│   └── lib/              # Utility functions
├── public/               # Static files (images, icons)
├── docs/                # Documentation (this folder)
├── package.json         # Project configuration
└── README.md           # Project overview
```

### Step 3: Install Dependencies

In your terminal, navigate to the portfolio folder and run:

```bash
npm install
```

This command:
- Reads the `package.json` file
- Downloads all required packages
- Creates a `node_modules` folder with dependencies

**What's happening?** npm is downloading all the tools and libraries our project needs, like React, Next.js, and Tailwind CSS.

### Step 4: Start the Development Server

Run this command:

```bash
npm run dev
```

You should see output like:
```
▲ Next.js 15.3.4 (Turbopack)
- Local:        http://localhost:3000
- Network:      http://*************:3000

✓ Ready in 2.1s
```

### Step 5: View Your Website

Open your web browser and go to: `http://localhost:3000`

You should see your portfolio website! 🎉

## 🎓 Understanding What Just Happened

### Node.js and npm
- **Node.js**: Allows JavaScript to run on your computer (not just in browsers)
- **npm**: Package manager that downloads and manages code libraries
- **package.json**: Configuration file that lists what our project needs

### Development Server
- **Local Development**: The website runs on your computer
- **Hot Reload**: Changes you make are automatically shown in the browser
- **Port 3000**: The address where your website is accessible locally

### Next.js
- **Framework**: Built on top of React to make development easier
- **File-based Routing**: Pages are created by adding files to the `app` folder
- **Automatic Optimization**: Makes your website fast automatically

## 🔍 Exploring the Code

### Key Files to Know

1. **`src/app/page.tsx`** - The main homepage
2. **`src/app/layout.tsx`** - Overall page structure
3. **`src/components/`** - Reusable pieces of the website
4. **`package.json`** - Project configuration and dependencies

### Making Your First Change

1. Open `src/app/page.tsx` in your code editor
2. Find the text "Your Name" 
3. Change it to your actual name
4. Save the file
5. Look at your browser - it should update automatically!

## 🚨 Common Issues and Solutions

### "Command not found" errors
- **Problem**: Node.js or npm not installed correctly
- **Solution**: Reinstall Node.js from the official website

### Port 3000 already in use
- **Problem**: Another application is using port 3000
- **Solution**: Stop other applications or use a different port:
  ```bash
  npm run dev -- --port 3001
  ```

### Permission errors on Mac/Linux
- **Problem**: npm doesn't have permission to install packages
- **Solution**: Use `sudo` (be careful) or configure npm properly

### Browser shows "This site can't be reached"
- **Problem**: Development server isn't running
- **Solution**: Make sure `npm run dev` is running in your terminal

## ✅ Success Checklist

Before moving to the next section, make sure:

- [ ] Node.js and npm are installed and working
- [ ] You can run `npm install` without errors
- [ ] The development server starts with `npm run dev`
- [ ] You can see the website at `http://localhost:3000`
- [ ] Changes you make to files appear in the browser
- [ ] Your code editor is set up and comfortable to use

## 🎯 What's Next?

Now that you have everything set up, let's learn about the technologies we're using in [Understanding the Technology Stack](./02-technology-stack.md).

## 💡 Pro Tips

1. **Keep the terminal open**: The development server needs to keep running
2. **Use VS Code**: It has excellent support for our technologies
3. **Save frequently**: Changes only appear after saving files
4. **Check the browser console**: Press F12 to see any error messages
5. **Don't be afraid to experiment**: You can always undo changes

Remember: Every professional developer started as a beginner. Take your time, experiment, and don't hesitate to look things up online!

## 📚 Additional Resources

- [Node.js Official Documentation](https://nodejs.org/docs/)
- [VS Code Getting Started](https://code.visualstudio.com/docs)
- [npm Documentation](https://docs.npmjs.com/)
- [Next.js Learn Course](https://nextjs.org/learn)
