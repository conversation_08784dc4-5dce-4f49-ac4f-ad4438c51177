# Contact Form Integration Guide

## 🎯 Overview

The contact section provides multiple ways for potential employers and collaborators to reach you. This guide explains how to integrate real email functionality, customize contact information, and optimize the contact experience.

## 📧 Email Integration Options

### Option 1: Netlify Forms (Recommended for Beginners)

**Pros:** Free, no backend required, spam protection
**Cons:** Only works on Netlify hosting

1. **Deploy to Netlify** (instead of Vercel)
2. **Add Netlify form attributes:**

```typescript
// In ContactForm.tsx, update the form element:
<form 
  onSubmit={handleSubmit} 
  className="space-y-6"
  name="contact"
  method="POST"
  data-netlify="true"
  data-netlify-honeypot="bot-field"
>
  {/* Add hidden fields for Netlify */}
  <input type="hidden" name="form-name" value="contact" />
  <input type="hidden" name="bot-field" />
  
  {/* Rest of your form fields */}
</form>
```

3. **Update form submission:**

```typescript
const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  
  if (!validateForm()) return;
  
  setIsSubmitting(true);
  
  try {
    const formData = new FormData();
    formData.append('form-name', 'contact');
    formData.append('name', formData.name);
    formData.append('email', formData.email);
    formData.append('subject', formData.subject);
    formData.append('message', formData.message);
    
    await fetch('/', {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams(formData).toString()
    });
    
    setSubmitStatus('success');
  } catch (error) {
    setSubmitStatus('error');
  } finally {
    setIsSubmitting(false);
  }
};
```

### Option 2: EmailJS (Works with Any Hosting)

**Pros:** Works with Vercel, free tier available, client-side only
**Cons:** Requires EmailJS account setup

1. **Install EmailJS:**
```bash
npm install @emailjs/browser
```

2. **Set up EmailJS account:**
   - Go to [emailjs.com](https://www.emailjs.com/)
   - Create account and email service
   - Get your Service ID, Template ID, and Public Key

3. **Update ContactForm.tsx:**

```typescript
import emailjs from '@emailjs/browser';

const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  
  if (!validateForm()) return;
  
  setIsSubmitting(true);
  
  try {
    await emailjs.send(
      'YOUR_SERVICE_ID',
      'YOUR_TEMPLATE_ID',
      {
        from_name: formData.name,
        from_email: formData.email,
        subject: formData.subject,
        message: formData.message,
      },
      'YOUR_PUBLIC_KEY'
    );
    
    setSubmitStatus('success');
    setFormData({ name: '', email: '', subject: '', message: '' });
  } catch (error) {
    setSubmitStatus('error');
  } finally {
    setIsSubmitting(false);
  }
};
```

4. **Create email template in EmailJS:**
```
Subject: New Portfolio Contact: {{subject}}

From: {{from_name}} ({{from_email}})
Subject: {{subject}}

Message:
{{message}}

---
Sent from your portfolio contact form
```

### Option 3: Custom API Route (Advanced)

**Pros:** Full control, can integrate with any email service
**Cons:** Requires backend knowledge

1. **Create API route:**

```typescript
// src/app/api/contact/route.ts
import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

export async function POST(request: NextRequest) {
  try {
    const { name, email, subject, message } = await request.json();
    
    // Create transporter (example with Gmail)
    const transporter = nodemailer.createTransporter({
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS, // Use app password
      },
    });
    
    // Send email
    await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to: '<EMAIL>',
      subject: `Portfolio Contact: ${subject}`,
      html: `
        <h3>New contact form submission</h3>
        <p><strong>Name:</strong> ${name}</p>
        <p><strong>Email:</strong> ${email}</p>
        <p><strong>Subject:</strong> ${subject}</p>
        <p><strong>Message:</strong></p>
        <p>${message}</p>
      `,
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to send email' }, { status: 500 });
  }
}
```

2. **Update ContactForm.tsx:**

```typescript
const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  
  if (!validateForm()) return;
  
  setIsSubmitting(true);
  
  try {
    const response = await fetch('/api/contact', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(formData),
    });
    
    if (response.ok) {
      setSubmitStatus('success');
      setFormData({ name: '', email: '', subject: '', message: '' });
    } else {
      setSubmitStatus('error');
    }
  } catch (error) {
    setSubmitStatus('error');
  } finally {
    setIsSubmitting(false);
  }
};
```

## 🔧 Customizing Contact Information

### Update Personal Details

In `ContactInfo.tsx`, update the contact methods:

```typescript
const contactMethods: ContactMethod[] = [
  {
    icon: <Mail className="w-6 h-6" />,
    label: 'Email',
    value: '<EMAIL>', // Your actual email
    href: 'mailto:<EMAIL>',
    description: 'Best way to reach me for professional inquiries'
  },
  {
    icon: <Phone className="w-6 h-6" />,
    label: 'Phone',
    value: '+64 21 123 4567', // Your actual phone
    href: 'tel:+6421234567',
    description: 'Available for calls during NZ business hours'
  },
  {
    icon: <MapPin className="w-6 h-6" />,
    label: 'Location',
    value: 'Auckland, New Zealand', // Your actual location
    href: 'https://maps.google.com/?q=Auckland,New+Zealand',
    description: 'Open to opportunities throughout New Zealand'
  }
];
```

### Update Social Media Links

```typescript
const socialLinks: SocialLink[] = [
  {
    name: 'GitHub',
    icon: <Github className="w-5 h-5" />,
    href: 'https://github.com/your-actual-username',
    username: '@your-actual-username',
    description: 'View my code and open source contributions'
  },
  {
    name: 'LinkedIn',
    icon: <Linkedin className="w-5 h-5" />,
    href: 'https://linkedin.com/in/your-actual-profile',
    username: '/in/your-actual-profile',
    description: 'Connect with me professionally'
  },
  // Add or remove social platforms as needed
];
```

## 📄 Resume Integration

### Adding Your Resume

1. **Create a professional PDF resume**
2. **Name it `resume.pdf`**
3. **Place it in the `public` folder**
4. **Update the download function:**

```typescript
const handleResumeDownload = () => {
  const link = document.createElement('a');
  link.href = '/resume.pdf';
  link.download = 'John_Smith_Resume.pdf'; // Use your actual name
  link.click();
};
```

### Resume Best Practices for NZ Market

**Structure (2 pages max):**
1. **Contact Information**
   - Full name, phone, email, LinkedIn
   - Location (city, country)
   - Visa status (if applicable)

2. **Professional Summary** (3-4 lines)
   - Recent graduate with Master's degree
   - Key technical skills
   - Career objectives

3. **Education**
   - Master of Applied Computing - Lincoln University (GPA if strong)
   - Bachelor's degree details
   - Relevant coursework

4. **Technical Skills**
   - Programming languages
   - Frameworks and tools
   - Cloud platforms
   - Databases

5. **Experience**
   - Internships
   - Part-time work
   - Volunteer experience
   - Academic projects

6. **Projects** (2-3 key projects)
   - Brief description
   - Technologies used
   - Impact/results

7. **Certifications**
   - AWS, Azure, Google certifications
   - Other relevant credentials

**NZ-Specific Tips:**
- Use NZ English spelling (colour, organisation, etc.)
- Include work rights status
- Mention local experience/projects
- Use metric measurements
- Include community involvement

## 🎨 Customizing the Contact Section

### Changing Colors and Styling

Update the color scheme in `ContactSection.tsx`:

```typescript
// Change primary color from blue to your brand color
const primaryColor = 'emerald'; // or 'purple', 'indigo', etc.

// Update button classes
className={`bg-${primaryColor}-600 hover:bg-${primaryColor}-700 text-white px-8 py-3 rounded-lg font-medium transition-colors`}
```

### Adding More Contact Methods

Add additional contact options:

```typescript
// Add to contactMethods array
{
  icon: <MessageSquare className="w-6 h-6" />,
  label: 'WhatsApp',
  value: '+64 21 XXX XXXX',
  href: 'https://wa.me/6421XXXXXXX',
  description: 'Quick messages and informal chats'
},
{
  icon: <Calendar className="w-6 h-6" />,
  label: 'Schedule Meeting',
  value: 'Book a call',
  href: 'https://calendly.com/your-username',
  description: 'Schedule a video call to discuss opportunities'
}
```

### Customizing FAQ Section

Update the FAQ content in `ContactSection.tsx`:

```typescript
const faqs = [
  {
    question: "What type of opportunities are you looking for?",
    answer: "I'm primarily seeking full-time software development roles in React, Node.js, and cloud technologies. I'm particularly interested in fintech, healthtech, or agritech companies."
  },
  {
    question: "Are you available for remote work?",
    answer: "Yes! I'm comfortable with remote work, hybrid arrangements, or on-site positions. I have experience collaborating effectively in distributed teams."
  },
  // Add more relevant questions
];
```

## 🔒 Security Considerations

### Form Validation

The contact form includes:
- **Client-side validation** for immediate feedback
- **Email format validation** using regex
- **Required field validation**
- **Minimum length requirements**

### Spam Protection

Add additional protection:

```typescript
// Add honeypot field (hidden from users)
<input
  type="text"
  name="website"
  style={{ display: 'none' }}
  tabIndex={-1}
  autoComplete="off"
/>

// Check honeypot in validation
if (formData.website) {
  // Likely spam, reject silently
  return;
}
```

### Rate Limiting

For API routes, add rate limiting:

```typescript
// Simple rate limiting example
const submissions = new Map();

export async function POST(request: NextRequest) {
  const ip = request.ip || 'unknown';
  const now = Date.now();
  const lastSubmission = submissions.get(ip);
  
  if (lastSubmission && now - lastSubmission < 60000) {
    return NextResponse.json({ error: 'Too many requests' }, { status: 429 });
  }
  
  submissions.set(ip, now);
  
  // Process form...
}
```

## 📊 Analytics and Tracking

### Track Form Submissions

Add analytics to track form usage:

```typescript
// Google Analytics example
const handleSubmit = async (e: React.FormEvent) => {
  // ... form submission logic
  
  if (submitStatus === 'success') {
    // Track successful submission
    gtag('event', 'form_submit', {
      event_category: 'contact',
      event_label: 'contact_form',
    });
  }
};
```

### Track Resume Downloads

```typescript
const handleResumeDownload = () => {
  // Track download
  gtag('event', 'file_download', {
    event_category: 'resume',
    event_label: 'resume_pdf',
  });
  
  // Download file
  const link = document.createElement('a');
  link.href = '/resume.pdf';
  link.download = 'John_Smith_Resume.pdf';
  link.click();
};
```

## 🚀 Testing Your Contact Form

### Manual Testing Checklist

- [ ] Form validation works for all fields
- [ ] Error messages display correctly
- [ ] Success message appears after submission
- [ ] Form resets after successful submission
- [ ] Email/phone links work correctly
- [ ] Social media links open in new tabs
- [ ] Resume download works
- [ ] Mobile responsiveness
- [ ] Dark mode compatibility

### Email Testing

1. **Test with real email addresses**
2. **Check spam folders**
3. **Verify email formatting**
4. **Test from different devices**
5. **Confirm auto-responses work**

## 🔧 Troubleshooting

### Common Issues

**Form not submitting:**
- Check network tab for errors
- Verify API endpoint is correct
- Check environment variables

**Emails not received:**
- Check spam folder
- Verify email service configuration
- Test with different email providers

**Resume not downloading:**
- Ensure file exists in public folder
- Check file permissions
- Test in different browsers

### Debug Mode

Add debug logging:

```typescript
const handleSubmit = async (e: React.FormEvent) => {
  console.log('Form data:', formData);
  
  try {
    const response = await fetch('/api/contact', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(formData),
    });
    
    console.log('Response status:', response.status);
    console.log('Response:', await response.text());
    
  } catch (error) {
    console.error('Submission error:', error);
  }
};
```

## 📱 Mobile Optimization

The contact section is fully responsive, but consider:

- **Touch-friendly form fields** (adequate spacing)
- **Mobile keyboard optimization** (input types)
- **Thumb-friendly buttons** (minimum 44px height)
- **Readable text sizes** (minimum 16px)

## 🎯 Conversion Optimization

### Improve Response Rates

1. **Clear value proposition** in contact section
2. **Multiple contact options** for different preferences
3. **Quick response time promise** (24 hours)
4. **Professional presentation** with portfolio highlights
5. **Social proof** through testimonials or recommendations

### A/B Testing Ideas

- Different contact form layouts
- Various call-to-action button texts
- Different contact method priorities
- Alternative color schemes

Remember to update all placeholder information with your actual contact details before deploying!
