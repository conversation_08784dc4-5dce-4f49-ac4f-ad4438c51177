# Customization Guide

## 🎯 Making the Portfolio Your Own

This guide will help you customize the portfolio to reflect your personal brand, skills, and projects. We'll cover everything from basic content changes to advanced customizations.

## 📝 Basic Content Customization

### 1. Personal Information

**File: `src/app/page.tsx`**

Replace placeholder content with your information:

```typescript
// Find this section and update:
<h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6 leading-tight">
  Hi, I'm{' '}
  <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent animate-gradient">
    <PERSON>  {/* ← Change this to your name */}
  </span>
</h1>

<h2 className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-6 font-medium">
  Master of Applied Computing Graduate  {/* ← Update your degree/title */}
</h2>

<p className="text-lg text-gray-500 dark:text-gray-400 mb-8 leading-relaxed">
  Passionate about cloud technologies, data analytics, cybersecurity, and full-stack development. 
  Building innovative solutions for New Zealand's tech landscape.
  {/* ← Write your own elevator pitch */}
</p>
```

### 2. Skills and Technologies

**File: `src/components/sections/SkillsSection.tsx`**

Update the skills data to match your expertise:

```typescript
// Find the skillCategories array and modify:
const skillCategories = [
  {
    id: 'cloud',
    name: 'Cloud Technologies',
    icon: Cloud,
    color: 'bg-blue-500',
    skills: [
      { name: 'AWS', level: 85, description: 'EC2, S3, Lambda, RDS' },  // ← Update levels
      { name: 'Microsoft Azure', level: 80, description: 'App Service, Functions, Storage' },
      // Add or remove skills as needed
    ],
  },
  // Add new categories or modify existing ones
]
```

### 3. Contact Information

**File: `src/components/layout/Footer.tsx`**

Update your contact details:

```typescript
// Update social links
const socialLinks = [
  {
    name: 'GitHub',
    href: 'https://github.com/yourusername',  // ← Your GitHub URL
    icon: Github,
  },
  {
    name: 'LinkedIn',
    href: 'https://linkedin.com/in/yourprofile',  // ← Your LinkedIn URL
    icon: Linkedin,
  },
  {
    name: 'Email',
    href: 'mailto:<EMAIL>',  // ← Your email
    icon: Mail,
  },
]

// Update location and email in the footer content
<span className="text-gray-300">New Zealand</span>  // ← Your location
<a href="mailto:<EMAIL>" className="text-gray-300 hover:text-white transition-colors">
  <EMAIL>  {/* ← Your email */}
</a>
```

### 4. SEO and Metadata

**File: `src/app/layout.tsx`**

Update the metadata for better SEO:

```typescript
export const metadata: Metadata = {
  title: "John Smith - Portfolio | Master of Applied Computing Graduate",  // ← Your name
  description: "Portfolio of John Smith, a Master of Applied Computing graduate from Lincoln University, New Zealand. Showcasing cloud technologies, data analytics, cybersecurity, and full-stack development projects.",  // ← Your description
  keywords: ["John Smith", "portfolio", "software developer", "cloud computing", "data analytics", "cybersecurity", "full-stack development", "New Zealand", "Lincoln University"],  // ← Add your name and relevant keywords
  authors: [{ name: "John Smith" }],  // ← Your name
  creator: "John Smith",  // ← Your name
  openGraph: {
    type: "website",
    locale: "en_NZ",
    url: "https://johnsmith-portfolio.vercel.app",  // ← Your domain
    title: "John Smith - Portfolio",  // ← Your name
    description: "Master of Applied Computing graduate showcasing technical projects and skills",
    siteName: "John Smith Portfolio",  // ← Your name
  },
  twitter: {
    card: "summary_large_image",
    title: "John Smith - Portfolio",  // ← Your name
    description: "Master of Applied Computing graduate showcasing technical projects and skills",
  },
}
```

## 🎨 Visual Customization

### 1. Color Scheme

**File: `tailwind.config.ts`**

Customize the color palette:

```typescript
module.exports = {
  theme: {
    extend: {
      colors: {
        // Add custom colors
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',  // Main blue
          600: '#2563eb',
          700: '#1d4ed8',
        },
        // Or use a different color scheme
        brand: {
          50: '#f0f9ff',
          500: '#06b6d4',  // Cyan
          600: '#0891b2',
          700: '#0e7490',
        }
      }
    }
  }
}
```

Then update components to use your colors:

```typescript
// Instead of bg-blue-600, use bg-primary-600 or bg-brand-600
<button className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-lg">
  View My Projects
</button>
```

### 2. Typography

**File: `src/app/globals.css`**

Add custom fonts:

```css
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

body {
  font-family: 'Inter', sans-serif;
}

/* Custom heading font */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
}
```

### 3. Professional Photo

**Replace the placeholder in the hero section:**

1. Add your professional photo to `public/images/profile.jpg`
2. Update the hero section in `src/app/page.tsx`:

```typescript
// Replace the placeholder div with:
<div className="w-full h-full rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center overflow-hidden">
  <img 
    src="/images/profile.jpg" 
    alt="John Smith - Professional Photo"
    className="w-full h-full object-cover"
  />
</div>
```

## 📊 Adding Your Projects

### 1. Create Project Data

**File: `src/data/projects.ts`**

```typescript
export interface Project {
  id: string
  title: string
  description: string
  longDescription: string
  technologies: string[]
  category: 'web' | 'mobile' | 'data' | 'cloud' | 'security'
  image: string
  liveUrl?: string
  githubUrl?: string
  featured: boolean
}

export const projects: Project[] = [
  {
    id: 'farm-management-system',
    title: 'Smart Farm Management System',
    description: 'IoT-enabled farm management platform for New Zealand agriculture',
    longDescription: 'A comprehensive farm management system that integrates IoT sensors, weather data, and machine learning to optimize crop yields and resource usage. Built for New Zealand farmers to monitor soil conditions, irrigation, and livestock.',
    technologies: ['React', 'Node.js', 'PostgreSQL', 'AWS IoT', 'Python', 'TensorFlow'],
    category: 'web',
    image: '/images/projects/farm-system.jpg',
    liveUrl: 'https://farm-demo.vercel.app',
    githubUrl: 'https://github.com/yourusername/farm-management',
    featured: true
  },
  {
    id: 'tourism-booking-app',
    title: 'NZ Tourism Booking Platform',
    description: 'Mobile app for booking New Zealand tourism experiences',
    longDescription: 'A React Native mobile application that allows tourists to discover and book unique New Zealand experiences. Features real-time availability, payment processing, and location-based recommendations.',
    technologies: ['React Native', 'TypeScript', 'Firebase', 'Stripe', 'Google Maps API'],
    category: 'mobile',
    image: '/images/projects/tourism-app.jpg',
    liveUrl: 'https://apps.apple.com/app/nz-tourism',
    githubUrl: 'https://github.com/yourusername/tourism-app',
    featured: true
  },
  // Add more projects...
]
```

### 2. Create Projects Section Component

**File: `src/components/sections/ProjectsSection.tsx`**

```typescript
import { projects } from '@/data/projects'
import ProjectCard from '@/components/ui/ProjectCard'

export default function ProjectsSection() {
  const featuredProjects = projects.filter(project => project.featured)
  
  return (
    <section id="projects" className="py-20 bg-white dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Featured Projects
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Real-world applications solving problems in agriculture, tourism, and business automation
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {featuredProjects.map((project, index) => (
            <ProjectCard 
              key={project.id} 
              project={project} 
              delay={index * 200}
            />
          ))}
        </div>
      </div>
    </section>
  )
}
```

## 🎯 Industry-Specific Customizations

### For New Zealand Job Market

1. **Highlight Local Relevance:**
```typescript
// Add NZ-specific keywords and projects
const nzFocusedSkills = [
  'Agricultural Technology',
  'Tourism Solutions', 
  'Government Digital Services',
  'Environmental Monitoring',
  'Small Business Automation'
]
```

2. **Use NZ Business Examples:**
```typescript
const projectDescriptions = {
  agriculture: "Built for Kiwi farmers to optimize dairy and sheep farming operations",
  tourism: "Helping tourists discover hidden gems across Aotearoa",
  business: "Supporting local SMEs with digital transformation"
}
```

### For Different Career Paths

**Data Analytics Focus:**
```typescript
const dataAnalyticsSkills = [
  { name: 'Python Data Science', level: 90, description: 'Pandas, NumPy, Scikit-learn' },
  { name: 'SQL & Databases', level: 85, description: 'PostgreSQL, MongoDB, BigQuery' },
  { name: 'Data Visualization', level: 80, description: 'Tableau, Power BI, D3.js' },
  { name: 'Machine Learning', level: 75, description: 'TensorFlow, PyTorch, MLflow' }
]
```

**Cloud Engineering Focus:**
```typescript
const cloudSkills = [
  { name: 'AWS Solutions', level: 90, description: 'EC2, Lambda, S3, RDS, CloudFormation' },
  { name: 'DevOps & CI/CD', level: 85, description: 'Docker, Kubernetes, Jenkins, GitLab' },
  { name: 'Infrastructure as Code', level: 80, description: 'Terraform, CloudFormation, Ansible' },
  { name: 'Monitoring & Logging', level: 75, description: 'CloudWatch, ELK Stack, Prometheus' }
]
```

## 🚀 Advanced Customizations

### 1. Add Dark Mode Toggle

**File: `src/components/ui/ThemeToggle.tsx`**

```typescript
'use client'
import { useState, useEffect } from 'react'
import { Sun, Moon } from 'lucide-react'

export default function ThemeToggle() {
  const [darkMode, setDarkMode] = useState(false)
  
  useEffect(() => {
    const isDark = localStorage.getItem('darkMode') === 'true'
    setDarkMode(isDark)
    document.documentElement.classList.toggle('dark', isDark)
  }, [])
  
  const toggleDarkMode = () => {
    const newDarkMode = !darkMode
    setDarkMode(newDarkMode)
    localStorage.setItem('darkMode', newDarkMode.toString())
    document.documentElement.classList.toggle('dark', newDarkMode)
  }
  
  return (
    <button
      onClick={toggleDarkMode}
      className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200"
    >
      {darkMode ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
    </button>
  )
}
```

### 2. Add Contact Form

**File: `src/components/forms/ContactForm.tsx`**

```typescript
'use client'
import { useState } from 'react'

export default function ContactForm() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  })
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    // Add form submission logic here
    console.log('Form submitted:', formData)
  }
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }
  
  return (
    <form onSubmit={handleSubmit} className="max-w-lg mx-auto">
      <div className="mb-4">
        <label className="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2">
          Name
        </label>
        <input
          type="text"
          name="name"
          value={formData.name}
          onChange={handleChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
          required
        />
      </div>
      
      <div className="mb-4">
        <label className="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2">
          Email
        </label>
        <input
          type="email"
          name="email"
          value={formData.email}
          onChange={handleChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
          required
        />
      </div>
      
      <div className="mb-6">
        <label className="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2">
          Message
        </label>
        <textarea
          name="message"
          value={formData.message}
          onChange={handleChange}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
          required
        />
      </div>
      
      <button
        type="submit"
        className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-colors"
      >
        Send Message
      </button>
    </form>
  )
}
```

## 📱 Responsive Design Tips

### 1. Mobile-First Approach
```typescript
// Use Tailwind's responsive prefixes
<div className="text-sm md:text-base lg:text-lg xl:text-xl">
  Responsive text
</div>

<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
  Responsive grid
</div>
```

### 2. Test on Different Devices
- Use browser dev tools to test mobile views
- Check on actual devices when possible
- Ensure touch targets are at least 44px

## 🎯 What's Next?

Once you've customized your portfolio:

1. **Test thoroughly** on different devices and browsers
2. **Get feedback** from friends, mentors, or career services
3. **Deploy to Vercel** following the [Deployment Guide](./08-deployment.md)
4. **Keep updating** with new projects and skills

## 📝 Customization Checklist

- [ ] Updated personal information (name, title, bio)
- [ ] Added your professional photo
- [ ] Customized skills and proficiency levels
- [ ] Updated contact information and social links
- [ ] Added your real projects with descriptions
- [ ] Customized colors and branding
- [ ] Updated SEO metadata
- [ ] Tested on mobile devices
- [ ] Proofread all content
- [ ] Added industry-specific keywords

Remember: Your portfolio should tell your unique story and showcase your best work. Make it authentically yours!
