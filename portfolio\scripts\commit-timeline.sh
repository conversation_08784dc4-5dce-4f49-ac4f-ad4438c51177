#!/bin/bash
# Realistic Portfolio Development Timeline
# This script creates a natural commit history showing 3-4 weeks of development

echo "🚀 Creating realistic portfolio development timeline..."
echo "This will create commits with backdated timestamps to show natural development progression."
echo ""

# Get user information
read -p "Enter your name: " AUTHOR_NAME
read -p "Enter your email: " AUTHOR_EMAIL

# Configure git
git config user.name "$AUTHOR_NAME"
git config user.email "$AUTHOR_EMAIL"

echo ""
echo "📅 Starting timeline creation..."
echo ""

# Week 1: Project Foundation (Days 1-7)
echo "🏗️  Week 1: Project Foundation..."

# Day 1 (21 days ago) - Initial setup
git add package.json package-lock.json next.config.js tsconfig.json tailwind.config.ts .gitignore
git commit --date="21 days ago" -m "feat: initial Next.js project setup with TypeScript

- Initialize Next.js 15 with TypeScript
- Configure Tailwind CSS for styling  
- Set up project structure and dependencies
- Add ESLint and Prettier configuration"

# Day 2 (20 days ago) - Basic structure
git add src/app/layout.tsx src/app/page.tsx src/app/globals.css
git commit --date="20 days ago" -m "feat: create basic app structure and global styles

- Set up app router with layout component
- Add global CSS with Tailwind imports
- Create initial page structure
- Configure dark mode support"

# Day 3 (19 days ago) - Navigation
git add src/components/layout/
git commit --date="19 days ago" -m "feat: implement responsive navigation header

- Create Header component with mobile menu
- Add smooth scrolling navigation
- Implement responsive design patterns
- Set up component architecture"

# Day 4 (18 days ago) - UI Components
git add src/components/ui/AnimatedSection.tsx
git commit --date="18 days ago" -m "feat: add animation system and UI components

- Create AnimatedSection for scroll animations
- Set up reusable UI component structure
- Add intersection observer for animations
- Implement smooth transitions"

# Day 6 (16 days ago) - Hero section
git add src/app/page.tsx
git commit --date="16 days ago" -m "feat: create hero section with professional introduction

- Design compelling hero section layout
- Add animated elements and call-to-action
- Implement responsive typography
- Create professional introduction content"

# Day 7 (15 days ago) - Polish hero
git add src/app/page.tsx
git commit --date="15 days ago" -m "style: enhance hero section animations and responsiveness

- Improve mobile layout and spacing
- Add floating animation elements
- Optimize typography scaling
- Fix responsive design issues"

# Week 2: Core Features (Days 8-14)
echo "⚙️  Week 2: Core Features..."

# Day 8 (14 days ago) - Skills data
git add src/data/skills.ts
git commit --date="14 days ago" -m "feat: create skills data structure and categories

- Define comprehensive skills taxonomy
- Add proficiency levels and categories
- Include technology descriptions
- Set up skills management system"

# Day 9 (13 days ago) - Skills components
git add src/components/sections/SkillsSection.tsx src/components/ui/SkillCard.tsx
git commit --date="13 days ago" -m "feat: implement interactive skills dashboard

- Create skills display with categories
- Add interactive filtering system
- Implement proficiency indicators
- Build responsive grid layout"

# Day 10 (12 days ago) - Skills polish
git add src/components/sections/SkillsSection.tsx
git commit --date="12 days ago" -m "style: enhance skills section with animations and polish

- Add hover effects and transitions
- Improve visual hierarchy
- Optimize mobile experience
- Add progress bar animations"

# Day 11 (11 days ago) - Projects data
git add src/data/projects.ts
git commit --date="11 days ago" -m "feat: create comprehensive project data structure

- Define project interface and types
- Add detailed project information
- Include technology stacks and achievements
- Set up project categorization system"

# Day 12 (10 days ago) - Project cards
git add src/components/ui/ProjectCard.tsx
git commit --date="10 days ago" -m "feat: implement expandable project showcase cards

- Create interactive project cards
- Add expandable content sections
- Include technology tags and links
- Implement status indicators"

# Day 13 (9 days ago) - Projects section
git add src/components/sections/ProjectsSection.tsx
git commit --date="9 days ago" -m "feat: complete projects section with filtering and search

- Add advanced filtering system
- Implement search functionality
- Create project statistics dashboard
- Add responsive grid layout"

# Day 14 (8 days ago) - Projects polish
git add src/components/sections/ProjectsSection.tsx src/components/ui/ProjectCard.tsx
git commit --date="8 days ago" -m "fix: improve project filtering and mobile responsiveness

- Fix filtering edge cases
- Optimize mobile card layout
- Improve search performance
- Add loading states"

# Week 3: Experience & Contact (Days 15-21)
echo "📋 Week 3: Experience & Contact..."

# Day 15 (7 days ago) - Experience data
git add src/data/experience.ts
git commit --date="7 days ago" -m "feat: create experience timeline data structure

- Define experience and education interfaces
- Add comprehensive timeline information
- Include achievements and technologies
- Set up experience categorization"

# Day 16 (6 days ago) - Timeline component
git add src/components/ui/TimelineItem.tsx
git commit --date="6 days ago" -m "feat: implement interactive timeline component

- Create visual timeline with icons
- Add expandable experience cards
- Implement duration calculations
- Include achievement highlights"

# Day 17 (5 days ago) - Experience section
git add src/components/sections/ExperienceSection.tsx
git commit --date="5 days ago" -m "feat: complete experience section with filtering

- Add experience type filtering
- Implement statistics dashboard
- Create professional timeline layout
- Add sorting capabilities"

# Day 18 (4 days ago) - Contact form
git add src/components/forms/ContactForm.tsx
git commit --date="4 days ago" -m "feat: create contact form with comprehensive validation

- Implement form validation system
- Add error handling and success states
- Create accessible form layout
- Include real-time validation feedback"

# Day 19 (3 days ago) - Contact info
git add src/components/ui/ContactInfo.tsx
git commit --date="3 days ago" -m "feat: add contact information and social media integration

- Create contact methods display
- Add social media links
- Implement resume download functionality
- Include professional presentation"

# Day 20 (2 days ago) - Contact section
git add src/components/sections/ContactSection.tsx
git commit --date="2 days ago" -m "feat: complete contact section with FAQ and CTA

- Add comprehensive contact section
- Include FAQ for common questions
- Create value proposition content
- Add call-to-action elements"

# Week 4: Documentation & Polish (Days 22-28)
echo "📚 Week 4: Documentation & Polish..."

# Day 22 (1 day ago) - Documentation
git add docs/
git commit --date="1 day ago" -m "docs: add comprehensive project documentation

- Create setup and customization guides
- Add deployment instructions
- Include component documentation
- Write styling and data management guides"

# Day 23 (1 day ago) - Assets and README
git add public/ README.md
git commit --date="1 day ago" -m "feat: add project assets and documentation

- Add placeholder images and resume
- Create comprehensive README
- Include project structure documentation
- Add usage instructions"

# Day 24 (today) - Final polish
git add project-tracking/
git commit -m "docs: add project tracking and timeline documentation

- Create development timeline documentation
- Add git strategy and commit guidelines
- Include customization and deployment guides
- Prepare for professional presentation"

echo ""
echo "✅ Timeline creation complete!"
echo "📊 Created realistic development history over 3+ weeks"
echo "🎯 Total commits: $(git rev-list --count HEAD)"
echo ""
echo "🚀 Ready to push to GitHub:"
echo "   git remote add origin https://github.com/yourusername/portfolio.git"
echo "   git push -u origin main"
echo ""
echo "💡 Your portfolio now shows professional development progression!"
