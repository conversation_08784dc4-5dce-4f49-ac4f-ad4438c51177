#!/bin/bash
# Progressive Portfolio Customization Commits
# Use this script to commit your customizations in realistic chunks

echo "🔄 Progressive Portfolio Customization"
echo "This script helps you commit your customizations in professional chunks."
echo ""

# Function to pause and wait for user
wait_for_user() {
    echo ""
    read -p "Press Enter when you've completed the changes described above..."
    echo ""
}

# Function to commit with message
commit_changes() {
    local message="$1"
    local description="$2"
    
    echo "📝 Committing: $message"
    git add .
    git commit -m "$message

$description"
    echo "✅ Committed successfully!"
    echo ""
}

echo "🎯 Step 1: Personalize Basic Information"
echo "Please update the following files with your real information:"
echo "  - src/app/page.tsx (hero section - your name, title, description)"
echo "  - src/app/layout.tsx (metadata - title, description, author)"
echo ""
wait_for_user

commit_changes "feat: personalize hero section and site metadata" "- Update name and professional title in hero section
- Customize professional summary and description
- Update site metadata with personal information
- Configure SEO tags with real details"

echo "🎯 Step 2: Update Contact Information"
echo "Please update your contact details:"
echo "  - src/components/ui/ContactInfo.tsx (email, phone, location, social media)"
echo "  - Add your real resume to public/resume.pdf"
echo ""
wait_for_user

commit_changes "feat: update contact information and add resume" "- Add real email address and phone number
- Update location and social media links
- Upload professional resume PDF
- Configure contact form with real details"

echo "🎯 Step 3: Customize Projects (Part 1)"
echo "Please update your project information:"
echo "  - src/data/projects.ts (update 2-3 projects with your real work)"
echo "  - Add project screenshots to public/images/projects/"
echo ""
wait_for_user

commit_changes "feat: add real projects and screenshots" "- Update project descriptions with actual work
- Add professional project screenshots
- Include real GitHub repository links
- Update technology stacks and achievements"

echo "🎯 Step 4: Customize Projects (Part 2)"
echo "Please complete your project customization:"
echo "  - src/data/projects.ts (finish remaining projects)"
echo "  - Add portfolio project as one of your projects"
echo ""
wait_for_user

commit_changes "feat: complete project showcase with portfolio project" "- Finish updating all project descriptions
- Add portfolio website as featured project
- Include comprehensive project details
- Update impact metrics and learnings"

echo "🎯 Step 5: Update Experience Timeline"
echo "Please customize your experience:"
echo "  - src/data/experience.ts (education, work experience, certifications)"
echo "  - Update with your actual timeline and achievements"
echo ""
wait_for_user

commit_changes "feat: customize experience timeline with real history" "- Update education details with actual degrees
- Add real work experience and internships
- Include actual certifications and achievements
- Customize skills and technologies learned"

echo "🎯 Step 6: Skills Assessment"
echo "Please review and adjust your skills:"
echo "  - src/data/skills.ts (adjust proficiency levels)"
echo "  - Update skill categories based on your experience"
echo ""
wait_for_user

commit_changes "feat: adjust skills assessment and proficiency levels" "- Update skill proficiency based on real experience
- Add or remove technologies as appropriate
- Adjust skill categories and descriptions
- Ensure accuracy in technical capabilities"

echo "🎯 Step 7: Final Polish and Optimization"
echo "Please make final improvements:"
echo "  - Review all content for accuracy and professionalism"
echo "  - Test all functionality (forms, links, downloads)"
echo "  - Optimize any remaining placeholder content"
echo ""
wait_for_user

commit_changes "feat: final polish and content optimization" "- Review and refine all content for professionalism
- Test contact form and resume download functionality
- Verify all external links work correctly
- Optimize content for job applications"

echo "🎯 Step 8: SEO and Performance (Optional)"
echo "If you want to add SEO improvements:"
echo "  - Add meta descriptions and Open Graph tags"
echo "  - Optimize images for web"
echo "  - Add sitemap or other SEO features"
echo ""
echo "Skip this step if you want to deploy now (y/n)?"
read -p "" skip_seo

if [ "$skip_seo" != "y" ] && [ "$skip_seo" != "Y" ]; then
    wait_for_user
    commit_changes "feat: add SEO optimization and performance improvements" "- Add comprehensive meta tags and Open Graph support
- Optimize images for web performance
- Implement SEO best practices
- Add structured data for better search visibility"
fi

echo ""
echo "🎉 Customization Complete!"
echo "📊 Your portfolio now has a realistic commit history showing:"
echo "   - Professional development workflow"
echo "   - Incremental feature development"
echo "   - Attention to detail and testing"
echo "   - Real-world project management"
echo ""
echo "🚀 Ready for deployment!"
echo "   1. Push to GitHub: git push origin main"
echo "   2. Deploy to Vercel"
echo "   3. Share with potential employers"
echo ""
echo "💼 Your portfolio demonstrates both technical skills and professional development practices!"
