'use client';

import { MessageCircle, Users, Clock, CheckCircle } from 'lucide-react';
import ContactForm from '@/components/forms/ContactForm';
import ContactInfo from '@/components/ui/ContactInfo';
import AnimatedSection from '@/components/ui/AnimatedSection';

export default function ContactSection() {
  const features = [
    {
      icon: <MessageCircle className="w-6 h-6" />,
      title: 'Quick Response',
      description: 'I typically respond to messages within 24 hours during business days.'
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: 'Collaboration Ready',
      description: 'Open to full-time roles, freelance projects, and exciting collaborations.'
    },
    {
      icon: <Clock className="w-6 h-6" />,
      title: 'Flexible Schedule',
      description: 'Available for meetings across different time zones and working arrangements.'
    },
    {
      icon: <CheckCircle className="w-6 h-6" />,
      title: 'Professional Approach',
      description: 'Committed to clear communication and delivering quality results.'
    }
  ];

  const reasons = [
    {
      title: 'Fresh Perspective',
      description: 'Recent graduate with up-to-date knowledge of modern technologies and industry best practices.'
    },
    {
      title: 'Strong Foundation',
      description: 'Master\'s degree in Applied Computing with hands-on experience in real-world projects.'
    },
    {
      title: 'Continuous Learning',
      description: 'Passionate about staying current with emerging technologies and professional development.'
    },
    {
      title: 'Problem Solver',
      description: 'Analytical mindset with experience tackling complex challenges in various domains.'
    },
    {
      title: 'Team Player',
      description: 'Collaborative approach with experience in both leadership and team member roles.'
    },
    {
      title: 'NZ Market Focus',
      description: 'Understanding of local business needs with projects tailored to New Zealand industries.'
    }
  ];

  return (
    <section id="contact" className="py-20 bg-white dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <AnimatedSection className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Let's Start a Conversation
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
            Ready to bring fresh ideas and technical expertise to your team. 
            Whether you're looking for a full-time developer, have a project in mind, 
            or just want to connect, I'd love to hear from you.
          </p>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-5xl mx-auto">
            {features.map((feature, index) => (
              <AnimatedSection key={feature.title} delay={index * 100}>
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 text-center">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mx-auto mb-3 text-blue-600 dark:text-blue-400">
                    {feature.icon}
                  </div>
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {feature.description}
                  </p>
                </div>
              </AnimatedSection>
            ))}
          </div>
        </AnimatedSection>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Contact Form - Takes up 2 columns */}
          <div className="lg:col-span-2">
            <AnimatedSection delay={200}>
              <ContactForm />
            </AnimatedSection>
          </div>

          {/* Contact Info - Takes up 1 column */}
          <div className="lg:col-span-1">
            <ContactInfo />
          </div>
        </div>

        {/* Why Work With Me Section */}
        <AnimatedSection delay={400} className="mt-20">
          <div className="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800 dark:to-blue-900/20 rounded-2xl p-8 lg:p-12">
            <div className="text-center mb-12">
              <h3 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4">
                Why Work With Me?
              </h3>
              <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                As a recent Master's graduate, I bring fresh energy, modern skills, 
                and a passion for creating innovative solutions.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {reasons.map((reason, index) => (
                <AnimatedSection key={reason.title} delay={600 + index * 100}>
                  <div className="bg-white dark:bg-gray-900 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                      {reason.title}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {reason.description}
                    </p>
                  </div>
                </AnimatedSection>
              ))}
            </div>
          </div>
        </AnimatedSection>

        {/* Call to Action */}
        <AnimatedSection delay={800} className="mt-16 text-center">
          <div className="bg-blue-600 rounded-2xl p-8 lg:p-12 text-white">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Ready to Get Started?
            </h3>
            <p className="text-blue-100 mb-8 max-w-2xl mx-auto">
              I'm excited about the opportunity to contribute to your team and help bring your ideas to life. 
              Let's discuss how my skills and enthusiasm can benefit your organization.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="mailto:<EMAIL>"
                className="bg-white text-blue-600 px-8 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors"
              >
                Send Email Directly
              </a>
              <a
                href="https://linkedin.com/in/yourprofile"
                target="_blank"
                rel="noopener noreferrer"
                className="border-2 border-white text-white px-8 py-3 rounded-lg font-medium hover:bg-white hover:text-blue-600 transition-colors"
              >
                Connect on LinkedIn
              </a>
            </div>

            <div className="mt-8 pt-8 border-t border-blue-500">
              <p className="text-blue-200 text-sm">
                🚀 Available for immediate start • 📍 Based in New Zealand • 🌏 Open to remote work
              </p>
            </div>
          </div>
        </AnimatedSection>

        {/* FAQ Section */}
        <AnimatedSection delay={1000} className="mt-20">
          <div className="text-center mb-12">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Frequently Asked Questions
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Quick answers to common questions about working together.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <div className="bg-white dark:bg-gray-900 rounded-lg p-6 shadow-sm">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                What type of opportunities are you looking for?
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                I'm primarily seeking full-time software development roles, but I'm also open to 
                freelance projects, internships, and collaborative opportunities that align with my skills.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-900 rounded-lg p-6 shadow-sm">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                Are you available for remote work?
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Yes! I'm comfortable with remote work, hybrid arrangements, or on-site positions. 
                I have experience collaborating effectively in distributed teams.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-900 rounded-lg p-6 shadow-sm">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                What's your preferred tech stack?
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                I specialize in React, TypeScript, Node.js, and cloud technologies (AWS/Azure). 
                However, I'm adaptable and eager to learn new technologies as needed.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-900 rounded-lg p-6 shadow-sm">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                How quickly can you start?
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                I'm available for immediate start for most opportunities. For larger commitments, 
                I can typically begin within 1-2 weeks to ensure a smooth transition.
              </p>
            </div>
          </div>
        </AnimatedSection>
      </div>
    </section>
  );
}
