# React Components Guide

## 🎯 What are React Components?

React components are like **reusable building blocks** for your website. Think of them as LEGO pieces - you create individual pieces and then combine them to build complex structures.

## 🧱 Component Basics

### What is a Component?

A component is a JavaScript function that returns HTML-like code (called JSX). It's a way to organize your code into small, manageable pieces.

**Simple Example:**
```typescript
function WelcomeMessage() {
  return <h1>Welcome to my portfolio!</h1>
}
```

**Using the Component:**
```typescript
function HomePage() {
  return (
    <div>
      <WelcomeMessage />
      <WelcomeMessage />
    </div>
  )
}
```

## 📦 Props: Passing Data to Components

Props (properties) are like **arguments** you pass to a function. They let you customize how a component behaves.

### Basic Props Example

```typescript
// Component that accepts props
function SkillCard({ skillName, level }: { skillName: string, level: number }) {
  return (
    <div className="skill-card">
      <h3>{skillName}</h3>
      <div>Level: {level}%</div>
    </div>
  )
}

// Using the component with different props
function SkillsPage() {
  return (
    <div>
      <SkillCard skillName="React" level={90} />
      <SkillCard skillName="TypeScript" level={85} />
      <SkillCard skillName="Python" level={80} />
    </div>
  )
}
```

### Interface for Props (TypeScript)

```typescript
// Define the shape of props
interface SkillCardProps {
  skillName: string
  level: number
  description?: string  // Optional prop
  isHighlighted?: boolean
}

// Use the interface
function SkillCard({ skillName, level, description, isHighlighted }: SkillCardProps) {
  return (
    <div className={`skill-card ${isHighlighted ? 'highlighted' : ''}`}>
      <h3>{skillName}</h3>
      <div>Level: {level}%</div>
      {description && <p>{description}</p>}
    </div>
  )
}
```

## 🔄 State: Making Components Interactive

State is like **memory** for your component. It remembers values that can change over time.

### useState Hook

```typescript
import { useState } from 'react'

function Counter() {
  // State: count starts at 0
  const [count, setCount] = useState(0)
  
  // Function to update state
  const increment = () => {
    setCount(count + 1)
  }
  
  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={increment}>Click me!</button>
    </div>
  )
}
```

### Real Example from Our Portfolio

```typescript
function SkillsSection() {
  // State to track which category is active
  const [activeCategory, setActiveCategory] = useState('cloud')
  
  return (
    <div>
      <button 
        onClick={() => setActiveCategory('cloud')}
        className={activeCategory === 'cloud' ? 'active' : ''}
      >
        Cloud Technologies
      </button>
      <button 
        onClick={() => setActiveCategory('frontend')}
        className={activeCategory === 'frontend' ? 'active' : ''}
      >
        Frontend
      </button>
      
      {/* Show different content based on active category */}
      {activeCategory === 'cloud' && <CloudSkills />}
      {activeCategory === 'frontend' && <FrontendSkills />}
    </div>
  )
}
```

## 🎨 Event Handling

Events are things that happen in your app - clicks, form submissions, etc.

### Common Event Types

```typescript
function ContactForm() {
  const [email, setEmail] = useState('')
  
  // Handle input changes
  const handleEmailChange = (event: ChangeEvent<HTMLInputElement>) => {
    setEmail(event.target.value)
  }
  
  // Handle form submission
  const handleSubmit = (event: FormEvent) => {
    event.preventDefault()  // Prevent page refresh
    console.log('Email:', email)
  }
  
  // Handle button clicks
  const handleButtonClick = () => {
    alert('Button clicked!')
  }
  
  return (
    <form onSubmit={handleSubmit}>
      <input 
        type="email" 
        value={email}
        onChange={handleEmailChange}
        placeholder="Enter your email"
      />
      <button type="submit">Submit</button>
      <button type="button" onClick={handleButtonClick}>
        Click Me
      </button>
    </form>
  )
}
```

## 🔄 useEffect: Side Effects

useEffect lets you do things **after** your component renders - like fetching data or setting up animations.

### Basic useEffect

```typescript
import { useState, useEffect } from 'react'

function UserProfile() {
  const [user, setUser] = useState(null)
  
  // Run once when component mounts
  useEffect(() => {
    console.log('Component mounted!')
    
    // Cleanup function (optional)
    return () => {
      console.log('Component unmounted!')
    }
  }, [])  // Empty array = run once
  
  // Run when user changes
  useEffect(() => {
    if (user) {
      console.log('User changed:', user)
    }
  }, [user])  // Run when 'user' changes
  
  return <div>User Profile</div>
}
```

## 📁 Component Organization in Our Portfolio

### File Structure
```
src/components/
├── layout/           # Layout components
│   ├── Header.tsx
│   ├── Footer.tsx
│   └── Layout.tsx
├── sections/         # Page sections
│   ├── SkillsSection.tsx
│   └── ProjectsSection.tsx
├── ui/              # Reusable UI components
│   ├── Button.tsx
│   ├── Card.tsx
│   └── AnimatedSection.tsx
└── forms/           # Form components
    └── ContactForm.tsx
```

### Example: Button Component

```typescript
// src/components/ui/Button.tsx
interface ButtonProps {
  children: React.ReactNode  // Button text/content
  onClick?: () => void
  variant?: 'primary' | 'secondary'
  disabled?: boolean
  className?: string
}

function Button({ 
  children, 
  onClick, 
  variant = 'primary', 
  disabled = false,
  className = ''
}: ButtonProps) {
  const baseClasses = 'px-4 py-2 rounded-lg font-medium transition-colors'
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700',
    secondary: 'border border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white'
  }
  
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
    >
      {children}
    </button>
  )
}

export default Button
```

### Using the Button Component

```typescript
import Button from '@/components/ui/Button'

function HomePage() {
  const handleClick = () => {
    alert('Button clicked!')
  }
  
  return (
    <div>
      <Button onClick={handleClick}>
        Primary Button
      </Button>
      
      <Button variant="secondary" onClick={handleClick}>
        Secondary Button
      </Button>
      
      <Button disabled>
        Disabled Button
      </Button>
    </div>
  )
}
```

## 🎯 Real Portfolio Examples

### 1. Skill Card Component

```typescript
interface Skill {
  name: string
  level: number
  description: string
}

interface SkillCardProps {
  skill: Skill
  delay?: number
}

function SkillCard({ skill, delay = 0 }: SkillCardProps) {
  return (
    <div 
      className="bg-white rounded-lg shadow-md p-6 transition-all duration-300 hover:shadow-lg"
      style={{ animationDelay: `${delay}ms` }}
    >
      <h3 className="text-xl font-bold mb-2">{skill.name}</h3>
      
      {/* Progress Bar */}
      <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
        <div 
          className="bg-blue-500 h-2 rounded-full transition-all duration-1000"
          style={{ width: `${skill.level}%` }}
        />
      </div>
      
      <span className="text-sm text-gray-600">{skill.level}%</span>
      <p className="text-gray-700 mt-2">{skill.description}</p>
    </div>
  )
}
```

### 2. Navigation Component

```typescript
function Navigation() {
  const [isOpen, setIsOpen] = useState(false)
  
  const navItems = [
    { name: 'Home', href: '#home' },
    { name: 'About', href: '#about' },
    { name: 'Skills', href: '#skills' },
    { name: 'Projects', href: '#projects' },
    { name: 'Contact', href: '#contact' }
  ]
  
  const scrollToSection = (href: string) => {
    const element = document.querySelector(href)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
    setIsOpen(false)  // Close mobile menu
  }
  
  return (
    <nav className="bg-white shadow-lg">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="text-xl font-bold">Portfolio</div>
          
          {/* Desktop Menu */}
          <div className="hidden md:flex space-x-4">
            {navItems.map((item) => (
              <button
                key={item.name}
                onClick={() => scrollToSection(item.href)}
                className="text-gray-700 hover:text-blue-600 px-3 py-2"
              >
                {item.name}
              </button>
            ))}
          </div>
          
          {/* Mobile Menu Button */}
          <button
            className="md:hidden"
            onClick={() => setIsOpen(!isOpen)}
          >
            {isOpen ? '✕' : '☰'}
          </button>
        </div>
        
        {/* Mobile Menu */}
        {isOpen && (
          <div className="md:hidden">
            {navItems.map((item) => (
              <button
                key={item.name}
                onClick={() => scrollToSection(item.href)}
                className="block w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100"
              >
                {item.name}
              </button>
            ))}
          </div>
        )}
      </div>
    </nav>
  )
}
```

## 💡 Best Practices

### 1. Keep Components Small and Focused
```typescript
// ❌ Too big - does too many things
function MegaComponent() {
  // 200 lines of code...
}

// ✅ Small and focused
function UserAvatar({ user }) { /* ... */ }
function UserInfo({ user }) { /* ... */ }
function UserActions({ user }) { /* ... */ }
```

### 2. Use Descriptive Names
```typescript
// ❌ Unclear
function Card() { /* ... */ }

// ✅ Clear and descriptive
function SkillCard() { /* ... */ }
function ProjectCard() { /* ... */ }
```

### 3. Extract Reusable Logic
```typescript
// Custom hook for form handling
function useForm(initialValues) {
  const [values, setValues] = useState(initialValues)
  
  const handleChange = (name, value) => {
    setValues(prev => ({ ...prev, [name]: value }))
  }
  
  return { values, handleChange }
}

// Use in multiple components
function ContactForm() {
  const { values, handleChange } = useForm({ name: '', email: '' })
  // ...
}
```

## 🎯 What's Next?

Now that you understand React components, let's learn about styling them with Tailwind CSS in [Styling with Tailwind CSS](./06-tailwind-css.md).

## 📝 Quick Reference

```typescript
// Basic component
function MyComponent() {
  return <div>Hello World</div>
}

// Component with props
function Greeting({ name }: { name: string }) {
  return <h1>Hello {name}!</h1>
}

// Component with state
function Counter() {
  const [count, setCount] = useState(0)
  return (
    <div>
      <p>{count}</p>
      <button onClick={() => setCount(count + 1)}>+</button>
    </div>
  )
}

// Component with effect
function DataLoader() {
  const [data, setData] = useState(null)
  
  useEffect(() => {
    // Load data when component mounts
    loadData().then(setData)
  }, [])
  
  return <div>{data ? 'Loaded!' : 'Loading...'}</div>
}
```

Remember: Start simple and build complexity gradually. Every expert was once a beginner!
