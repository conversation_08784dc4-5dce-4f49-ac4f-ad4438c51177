export interface ExperienceItem {
  id: string
  title: string
  organization: string
  location: string
  startDate: string
  endDate: string | 'Present'
  type: 'education' | 'work' | 'internship' | 'certification' | 'project' | 'volunteer'
  description: string
  achievements?: string[]
  technologies?: string[]
  grade?: string
  coursework?: string[]
  skills?: string[]
  logo?: string
  website?: string
  featured: boolean
}

export const experienceData: ExperienceItem[] = [
  {
    id: 'lincoln-university-mac',
    title: 'Master of Applied Computing',
    organization: 'Lincoln University',
    location: 'Canterbury, New Zealand',
    startDate: '2023-02',
    endDate: '2024-12',
    type: 'education',
    description: 'Comprehensive postgraduate program focusing on practical computing applications, software development, data analytics, and emerging technologies. Specialized in cloud computing, cybersecurity, and full-stack development with emphasis on real-world industry applications.',
    achievements: [
      'Graduated with Distinction (GPA: 8.5/9.0)',
      'Dean\'s List for Academic Excellence (2023, 2024)',
      'Best Project Award for Smart Farm Management System',
      'Research Assistant for Agricultural Technology Lab',
      'President of Computing Students Association'
    ],
    coursework: [
      'Advanced Software Engineering',
      'Cloud Computing & DevOps',
      'Data Analytics & Machine Learning',
      'Cybersecurity & Network Security',
      'Mobile Application Development',
      'Database Systems & Big Data',
      'Human-Computer Interaction',
      'Project Management & Agile Methods'
    ],
    technologies: ['Python', 'Java', 'JavaScript', 'React', 'Node.js', 'AWS', 'Docker', 'PostgreSQL', 'MongoDB', 'TensorFlow'],
    skills: ['Software Development', 'Data Analysis', 'Cloud Architecture', 'Project Management', 'Research', 'Technical Writing'],
    website: 'https://www.lincoln.ac.nz',
    featured: true
  },
  {
    id: 'tech-internship',
    title: 'Software Development Intern',
    organization: 'TechStart NZ',
    location: 'Christchurch, New Zealand',
    startDate: '2024-06',
    endDate: '2024-08',
    type: 'internship',
    description: 'Summer internship at a growing tech startup focused on agricultural technology solutions. Worked on developing web applications for farm management and contributed to mobile app development for livestock monitoring.',
    achievements: [
      'Developed 3 key features for the main farm management platform',
      'Improved application performance by 40% through code optimization',
      'Led a team of 2 junior interns on mobile app development',
      'Presented final project to company stakeholders and investors',
      'Received offer for full-time position upon graduation'
    ],
    technologies: ['React', 'TypeScript', 'Node.js', 'Express', 'PostgreSQL', 'AWS', 'React Native', 'Git'],
    skills: ['Full-Stack Development', 'Team Leadership', 'Agile Development', 'Code Review', 'Client Communication'],
    featured: true
  },
  {
    id: 'aws-cloud-practitioner',
    title: 'AWS Certified Cloud Practitioner',
    organization: 'Amazon Web Services',
    location: 'Online',
    startDate: '2024-03',
    endDate: '2024-03',
    type: 'certification',
    description: 'Foundational certification demonstrating understanding of AWS cloud concepts, services, security, architecture, pricing, and support. Validates ability to define what the AWS Cloud is and the basic global infrastructure.',
    achievements: [
      'Passed with score of 890/1000 (85%)',
      'Completed 40+ hours of study and hands-on labs',
      'Demonstrated knowledge of core AWS services',
      'Understanding of cloud economics and billing'
    ],
    technologies: ['AWS', 'EC2', 'S3', 'RDS', 'Lambda', 'CloudFormation', 'IAM', 'VPC'],
    skills: ['Cloud Computing', 'AWS Services', 'Cloud Security', 'Cost Optimization'],
    website: 'https://aws.amazon.com/certification/',
    featured: true
  },
  {
    id: 'bachelor-computer-science',
    title: 'Bachelor of Computer Science',
    organization: 'University of Canterbury',
    location: 'Christchurch, New Zealand',
    startDate: '2020-02',
    endDate: '2022-12',
    type: 'education',
    description: 'Comprehensive undergraduate program covering fundamental computer science concepts, programming, algorithms, data structures, and software engineering principles. Strong foundation in mathematics and computational thinking.',
    grade: 'First Class Honours (GPA: 8.2/9.0)',
    achievements: [
      'First Class Honours with Distinction',
      'Computer Science Prize for Outstanding Academic Achievement',
      'Tutor for COSC122 (Introduction to Computer Science)',
      'Member of Programming Competition Team (ACM ICPC)',
      'Volunteer at Hour of Code events for local schools'
    ],
    coursework: [
      'Data Structures & Algorithms',
      'Software Engineering',
      'Database Systems',
      'Computer Networks',
      'Operating Systems',
      'Artificial Intelligence',
      'Computer Graphics',
      'Discrete Mathematics'
    ],
    technologies: ['Java', 'Python', 'C++', 'SQL', 'HTML/CSS', 'JavaScript', 'Git', 'Linux'],
    skills: ['Programming', 'Algorithm Design', 'Problem Solving', 'Mathematical Analysis', 'Technical Communication'],
    website: 'https://www.canterbury.ac.nz',
    featured: false
  },
  {
    id: 'microsoft-azure-fundamentals',
    title: 'Microsoft Azure Fundamentals (AZ-900)',
    organization: 'Microsoft',
    location: 'Online',
    startDate: '2024-01',
    endDate: '2024-01',
    type: 'certification',
    description: 'Foundational certification covering Azure cloud concepts, core Azure services, security, privacy, compliance, trust, and Azure pricing and support.',
    achievements: [
      'Passed with score of 850/1000',
      'Demonstrated understanding of cloud concepts',
      'Knowledge of Azure core services and solutions',
      'Understanding of Azure security and compliance'
    ],
    technologies: ['Microsoft Azure', 'Azure Active Directory', 'Azure Storage', 'Azure Compute', 'Azure Networking'],
    skills: ['Cloud Computing', 'Azure Services', 'Cloud Security', 'Identity Management'],
    website: 'https://docs.microsoft.com/en-us/learn/certifications/azure-fundamentals/',
    featured: false
  },
  {
    id: 'part-time-developer',
    title: 'Part-time Web Developer',
    organization: 'Local Business Solutions',
    location: 'Christchurch, New Zealand',
    startDate: '2023-03',
    endDate: '2024-02',
    type: 'work',
    description: 'Part-time role developing websites and web applications for local small businesses while completing Master\'s degree. Focused on creating responsive, user-friendly websites that help businesses establish their online presence.',
    achievements: [
      'Developed 8+ websites for local businesses',
      'Improved client website loading speeds by average of 60%',
      'Implemented SEO best practices resulting in 40% increase in organic traffic',
      'Managed client relationships and project timelines independently',
      'Trained 2 junior developers in modern web development practices'
    ],
    technologies: ['React', 'Next.js', 'WordPress', 'PHP', 'MySQL', 'HTML/CSS', 'JavaScript', 'Figma'],
    skills: ['Web Development', 'Client Management', 'SEO Optimization', 'Responsive Design', 'Project Management'],
    featured: false
  },
  {
    id: 'google-analytics-certified',
    title: 'Google Analytics Certified',
    organization: 'Google',
    location: 'Online',
    startDate: '2023-11',
    endDate: '2023-11',
    type: 'certification',
    description: 'Certification demonstrating proficiency in Google Analytics, including how to set up and configure Analytics accounts, implement tracking code, analyze reports, and set up goals and campaign tracking.',
    achievements: [
      'Passed Google Analytics Individual Qualification (IQ)',
      'Demonstrated expertise in web analytics',
      'Understanding of conversion tracking and attribution',
      'Knowledge of audience analysis and reporting'
    ],
    technologies: ['Google Analytics', 'Google Tag Manager', 'Google Ads', 'Data Studio'],
    skills: ['Web Analytics', 'Data Analysis', 'Digital Marketing', 'Conversion Optimization'],
    website: 'https://skillshop.exceedlms.com/student/catalog/list?category_ids=53-google-analytics-4',
    featured: false
  },
  {
    id: 'volunteer-coding-instructor',
    title: 'Volunteer Coding Instructor',
    organization: 'Code Club Aotearoa',
    location: 'Christchurch, New Zealand',
    startDate: '2022-06',
    endDate: 'Present',
    type: 'volunteer',
    description: 'Volunteer instructor teaching programming fundamentals to children aged 9-13 at local schools and community centers. Focus on making coding accessible and fun through interactive projects and games.',
    achievements: [
      'Taught 50+ children basic programming concepts',
      'Developed curriculum for Scratch and Python workshops',
      'Organized coding competitions and showcase events',
      'Mentored 5 students who went on to pursue computer science',
      'Received Community Volunteer Award 2023'
    ],
    technologies: ['Scratch', 'Python', 'HTML/CSS', 'JavaScript', 'Raspberry Pi'],
    skills: ['Teaching', 'Curriculum Development', 'Public Speaking', 'Mentoring', 'Community Engagement'],
    website: 'https://codeclub.nz',
    featured: false
  }
]

export const experienceTypes = [
  { id: 'all', name: 'All Experience', icon: '📋', color: 'bg-gray-500' },
  { id: 'education', name: 'Education', icon: '🎓', color: 'bg-blue-500' },
  { id: 'work', name: 'Work Experience', icon: '💼', color: 'bg-green-500' },
  { id: 'internship', name: 'Internships', icon: '🚀', color: 'bg-purple-500' },
  { id: 'certification', name: 'Certifications', icon: '🏆', color: 'bg-yellow-500' },
  { id: 'volunteer', name: 'Volunteer Work', icon: '❤️', color: 'bg-red-500' },
]

export const getExperienceIcon = (type: string): string => {
  const typeMap: { [key: string]: string } = {
    education: '🎓',
    work: '💼',
    internship: '🚀',
    certification: '🏆',
    project: '💻',
    volunteer: '❤️'
  }
  return typeMap[type] || '📋'
}

export const getExperienceColor = (type: string): string => {
  const colorMap: { [key: string]: string } = {
    education: 'bg-blue-500',
    work: 'bg-green-500',
    internship: 'bg-purple-500',
    certification: 'bg-yellow-500',
    project: 'bg-indigo-500',
    volunteer: 'bg-red-500'
  }
  return colorMap[type] || 'bg-gray-500'
}
