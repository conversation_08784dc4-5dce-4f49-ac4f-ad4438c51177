# PowerShell Script to Fix Git Author History
# This script changes all commits from "<PERSON>" to "Tank"

Write-Host "🔧 Fixing commit author history..." -ForegroundColor Green
Write-Host "Changing all commits from '<PERSON>' to 'Tank'" -ForegroundColor Yellow
Write-Host ""

# Set environment variable to suppress filter-branch warning
$env:FILTER_BRANCH_SQUELCH_WARNING = "1"

# Run git filter-branch to change author
git filter-branch --env-filter '
if [ "$GIT_AUTHOR_NAME" = "<PERSON>" ]; then
    export GIT_AUTHOR_NAME="Tank"
    export GIT_AUTHOR_EMAIL="<EMAIL>"
fi
if [ "$GIT_COMMITTER_NAME" = "<PERSON>" ]; then
    export GIT_COMMITTER_NAME="Tank"
    export GIT_COMMITTER_EMAIL="<EMAIL>"
fi
' -- --all

Write-Host ""
Write-Host "✅ Author history updated!" -ForegroundColor Green
Write-Host "🚀 Now force push to GitHub:" -ForegroundColor Yellow
Write-Host "   git push --force-with-lease origin main" -ForegroundColor Cyan
Write-Host ""
Write-Host "⚠️  This will update your GitHub repository with the corrected author information" -ForegroundColor Red
