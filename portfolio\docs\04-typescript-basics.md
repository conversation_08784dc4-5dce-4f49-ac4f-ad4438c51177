# TypeScript Basics for Beginners

## 🎯 What is TypeScript?

TypeScript is JavaScript with **types**. Think of types as labels that tell you what kind of data you're working with.

**Analogy**: Imagine organizing a toolbox
- **JavaScript**: All tools thrown in randomly - you find out what each tool is when you pick it up
- **TypeScript**: Each tool has a clear label - you know exactly what you're getting

## 🔍 Why Use TypeScript?

### Before TypeScript (JavaScript)
```javascript
function calculateTotal(price, tax) {
  return price + tax
}

calculateTotal("50", "5") // Returns "505" (string concatenation!)
calculateTotal(50, 5)    // Returns 55 (number addition)
```

### With TypeScript
```typescript
function calculateTotal(price: number, tax: number): number {
  return price + tax
}

calculateTotal("50", "5") // ❌ TypeScript Error: Arguments must be numbers
calculateTotal(50, 5)     // ✅ Returns 55
```

## 📚 Basic Types

### Primitive Types

```typescript
// String - text data
let name: string = "<PERSON>"
let email: string = "<EMAIL>"

// Number - numeric data
let age: number = 25
let price: number = 99.99

// Boolean - true/false
let isStudent: boolean = true
let hasExperience: boolean = false

// Array - list of items
let skills: string[] = ["React", "TypeScript", "Node.js"]
let scores: number[] = [85, 90, 78, 92]

// Alternative array syntax
let languages: Array<string> = ["JavaScript", "Python", "C#"]
```

### Object Types

```typescript
// Object with specific properties
let person: {
  name: string
  age: number
  isEmployed: boolean
} = {
  name: "Sarah",
  age: 28,
  isEmployed: true
}

// Optional properties (marked with ?)
let user: {
  name: string
  email: string
  phone?: string  // Optional - might not exist
} = {
  name: "Mike",
  email: "<EMAIL>"
  // phone is optional, so we can omit it
}
```

## 🏗️ Interfaces: Reusable Type Definitions

Instead of writing object types repeatedly, we use interfaces:

```typescript
// Define the structure once
interface Skill {
  name: string
  level: number
  description: string
  isCore?: boolean  // Optional property
}

// Use it multiple times
let reactSkill: Skill = {
  name: "React",
  level: 90,
  description: "Frontend library for building UIs"
}

let pythonSkill: Skill = {
  name: "Python",
  level: 85,
  description: "Programming language for data science",
  isCore: true
}

// Array of skills
let allSkills: Skill[] = [reactSkill, pythonSkill]
```

## 🔧 Functions with Types

### Basic Function Typing

```typescript
// Function that takes parameters and returns a value
function greetUser(name: string, age: number): string {
  return `Hello ${name}, you are ${age} years old`
}

// Function with optional parameter
function createUser(name: string, email: string, phone?: string): void {
  console.log(`Creating user: ${name}`)
  // void means this function doesn't return anything
}

// Arrow function with types
const calculatePercentage = (value: number, total: number): number => {
  return (value / total) * 100
}
```

### Function as Parameters

```typescript
// Function that takes another function as parameter
function processSkills(
  skills: Skill[], 
  processor: (skill: Skill) => string
): string[] {
  return skills.map(processor)
}

// Using it
const skillNames = processSkills(allSkills, (skill) => skill.name)
// Result: ["React", "Python"]
```

## 🎨 Real Examples from Our Portfolio

### Component Props Interface

```typescript
// Define what props our component expects
interface SkillCardProps {
  skill: {
    name: string
    level: number
    description: string
  }
  delay: number
}

// Use in component
function SkillCard({ skill, delay }: SkillCardProps) {
  return (
    <div className="skill-card">
      <h3>{skill.name}</h3>
      <div>Level: {skill.level}%</div>
      <p>{skill.description}</p>
    </div>
  )
}
```

### State with Types

```typescript
import { useState } from 'react'

// Define the shape of our state
interface User {
  name: string
  email: string
  skills: string[]
}

function UserProfile() {
  // TypeScript knows user is of type User or null
  const [user, setUser] = useState<User | null>(null)
  
  // TypeScript knows activeTab is a string
  const [activeTab, setActiveTab] = useState<string>('profile')
  
  return (
    <div>
      {user ? (
        <h1>Welcome, {user.name}!</h1>
      ) : (
        <h1>Please log in</h1>
      )}
    </div>
  )
}
```

### Event Handlers

```typescript
import { ChangeEvent, MouseEvent } from 'react'

function ContactForm() {
  // Handle input changes
  const handleInputChange = (event: ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value
    console.log('Input changed:', value)
  }
  
  // Handle button clicks
  const handleSubmit = (event: MouseEvent<HTMLButtonElement>) => {
    event.preventDefault()
    console.log('Form submitted!')
  }
  
  return (
    <form>
      <input onChange={handleInputChange} />
      <button onClick={handleSubmit}>Submit</button>
    </form>
  )
}
```

## 🔄 Union Types: Multiple Possibilities

```typescript
// A value can be one of several types
type Status = 'loading' | 'success' | 'error'

let currentStatus: Status = 'loading'
currentStatus = 'success'  // ✅ Valid
currentStatus = 'pending'  // ❌ Error: not in the union

// Function with union type parameter
function displayMessage(status: Status): string {
  switch (status) {
    case 'loading':
      return 'Please wait...'
    case 'success':
      return 'Operation completed!'
    case 'error':
      return 'Something went wrong'
  }
}
```

## 🎯 Practical Exercise: Building a Skill Component

Let's build a skill component step by step:

### Step 1: Define the Data Structure

```typescript
interface Skill {
  name: string
  level: number  // 0-100
  category: 'frontend' | 'backend' | 'database' | 'cloud'
  description: string
  isHighlighted?: boolean
}
```

### Step 2: Create Sample Data

```typescript
const mySkills: Skill[] = [
  {
    name: "React",
    level: 90,
    category: "frontend",
    description: "Building interactive user interfaces",
    isHighlighted: true
  },
  {
    name: "Node.js",
    level: 75,
    category: "backend", 
    description: "Server-side JavaScript development"
  },
  {
    name: "AWS",
    level: 80,
    category: "cloud",
    description: "Cloud computing and deployment"
  }
]
```

### Step 3: Create the Component

```typescript
interface SkillCardProps {
  skill: Skill
  onClick?: (skillName: string) => void
}

function SkillCard({ skill, onClick }: SkillCardProps) {
  const handleClick = () => {
    if (onClick) {
      onClick(skill.name)
    }
  }
  
  return (
    <div 
      className={`skill-card ${skill.isHighlighted ? 'highlighted' : ''}`}
      onClick={handleClick}
    >
      <h3>{skill.name}</h3>
      <div className="progress-bar">
        <div 
          className="progress-fill"
          style={{ width: `${skill.level}%` }}
        />
      </div>
      <span className="category">{skill.category}</span>
      <p>{skill.description}</p>
    </div>
  )
}
```

### Step 4: Use the Component

```typescript
function SkillsSection() {
  const handleSkillClick = (skillName: string) => {
    console.log(`Clicked on ${skillName}`)
  }
  
  return (
    <div className="skills-grid">
      {mySkills.map((skill) => (
        <SkillCard 
          key={skill.name}
          skill={skill}
          onClick={handleSkillClick}
        />
      ))}
    </div>
  )
}
```

## 🚨 Common TypeScript Errors and Solutions

### Error: Property doesn't exist

```typescript
// ❌ Error
interface User {
  name: string
  email: string
}

const user: User = { name: "John" }  // Missing email

// ✅ Solution
const user: User = { 
  name: "John",
  email: "<EMAIL>"
}
```

### Error: Type mismatch

```typescript
// ❌ Error
let age: number = "25"  // String assigned to number

// ✅ Solution
let age: number = 25
// or
let age: number = parseInt("25")
```

### Error: Cannot read property of undefined

```typescript
// ❌ Potential error
interface User {
  profile?: {
    avatar: string
  }
}

const user: User = {}
console.log(user.profile.avatar)  // Error if profile is undefined

// ✅ Solution: Optional chaining
console.log(user.profile?.avatar)  // Returns undefined safely
```

## 💡 TypeScript Best Practices

1. **Start with basic types**, add complexity gradually
2. **Use interfaces** for object shapes you'll reuse
3. **Make properties optional** with `?` when they might not exist
4. **Use union types** for values that can be one of several options
5. **Let TypeScript infer types** when it's obvious
6. **Use meaningful names** for interfaces and types

## 🎯 What's Next?

Now that you understand TypeScript basics, let's learn how to build React components in [React Components](./05-react-components.md).

## 📝 Quick Reference

```typescript
// Basic types
let name: string = "John"
let age: number = 25
let isActive: boolean = true
let skills: string[] = ["React", "TypeScript"]

// Interface
interface Person {
  name: string
  age: number
  email?: string  // Optional
}

// Function
function greet(person: Person): string {
  return `Hello ${person.name}`
}

// Component props
interface ButtonProps {
  text: string
  onClick: () => void
  disabled?: boolean
}
```

Remember: TypeScript is there to help you write better code. Don't fight it - embrace the type safety!
