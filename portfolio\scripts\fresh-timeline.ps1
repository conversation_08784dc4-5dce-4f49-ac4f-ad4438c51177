# PowerShell Script for Fresh Portfolio Timeline with Correct Author
# This script creates a clean timeline with <PERSON> as the author

Write-Host "🚀 Creating fresh timeline with correct author..." -ForegroundColor Green
Write-Host "Author: Tank (<EMAIL>)" -ForegroundColor Yellow
Write-Host ""

# Reset to clean state
Write-Host "🧹 Cleaning up previous timeline..." -ForegroundColor Blue
git reset --hard HEAD~17  # Go back before our timeline commits
git clean -fd

# Configure git with correct information
git config user.name "Tank"
git config user.email "<EMAIL>"

Write-Host "📅 Creating new timeline..." -ForegroundColor Cyan

# Week 1: Project Foundation
Write-Host "🏗️  Week 1: Project Foundation..." -ForegroundColor Blue

# Day 1 (7 days ago) - Initial setup
git add package.json, package-lock.json, next.config.ts, tsconfig.json, .gitignore
git commit --date="7 days ago" -m "feat: initial Next.js project setup with TypeScript

- Initialize Next.js 15 with TypeScript
- Configure Tailwind CSS for styling  
- Set up project structure and dependencies
- Add ESLint and Prettier configuration"

# Day 2 (6 days ago) - Basic structure
git add src/app/layout.tsx, src/app/page.tsx, src/app/globals.css
git commit --date="6 days ago" -m "feat: create basic app structure and global styles

- Set up app router with layout component
- Add global CSS with Tailwind imports
- Create initial page structure
- Configure dark mode support"

# Day 3 (5 days ago) - Navigation and UI
git add src/components/layout/, src/components/ui/AnimatedSection.tsx
git commit --date="5 days ago" -m "feat: implement responsive navigation and UI components

- Create Header component with mobile menu
- Add smooth scrolling navigation
- Implement animation system
- Set up component architecture"

# Day 4 (4 days ago) - Skills and Projects Data
git add src/data/skills.ts, src/data/projects.ts
git commit --date="4 days ago" -m "feat: create comprehensive data structures

- Define skills taxonomy with proficiency levels
- Add detailed project information
- Include technology stacks and achievements
- Set up categorization systems"

# Day 5 (3 days ago) - Core Components
git add src/components/sections/SkillsSection.tsx, src/components/ui/ProjectCard.tsx, src/components/sections/ProjectsSection.tsx
git commit --date="3 days ago" -m "feat: implement skills dashboard and project showcase

- Create interactive skills display with filtering
- Build expandable project cards
- Add search and filtering functionality
- Implement responsive grid layouts"

# Day 6 (2 days ago) - Experience and Contact
git add src/data/experience.ts, src/components/ui/TimelineItem.tsx, src/components/sections/ExperienceSection.tsx, src/components/forms/ContactForm.tsx, src/components/ui/ContactInfo.tsx, src/components/sections/ContactSection.tsx
git commit --date="2 days ago" -m "feat: complete experience timeline and contact section

- Create interactive timeline component
- Add experience filtering and statistics
- Implement contact form with validation
- Include social media integration"

# Day 7 (1 day ago) - Documentation and Polish
git add docs/, public/, .prettierrc, src/lib/
git commit --date="1 day ago" -m "docs: add comprehensive documentation and assets

- Create setup and customization guides
- Add deployment instructions
- Include project assets and utilities
- Prepare for professional presentation"

# Today - Final touches
git add project-tracking/, scripts/
git commit -m "feat: add project tracking and deployment scripts

- Create development timeline documentation
- Add git strategy and commit guidelines
- Include customization and deployment guides
- Complete professional portfolio setup"

Write-Host ""
Write-Host "✅ Fresh timeline created!" -ForegroundColor Green
Write-Host "📊 All commits now show 'Tank' as author" -ForegroundColor Yellow
$commitCount = git rev-list --count HEAD
Write-Host "🎯 Total commits: $commitCount" -ForegroundColor Cyan
Write-Host ""
Write-Host "🚀 Ready for GitHub:" -ForegroundColor Green
Write-Host "   1. Delete old repository on GitHub" -ForegroundColor White
Write-Host "   2. Create new repository: https://github.com/TankHao12/portfolio" -ForegroundColor White
Write-Host "   3. git remote add origin https://github.com/TankHao12/portfolio.git" -ForegroundColor White
Write-Host "   4. git push -u origin main" -ForegroundColor White
