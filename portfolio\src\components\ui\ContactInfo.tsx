'use client';

import { Mail, Phone, MapPin, Download, ExternalLink, Github, Linkedin, Twitter } from 'lucide-react';
import AnimatedSection from './AnimatedSection';

interface ContactMethod {
  icon: React.ReactNode;
  label: string;
  value: string;
  href: string;
  description: string;
}

interface SocialLink {
  name: string;
  icon: React.ReactNode;
  href: string;
  username: string;
  description: string;
}

export default function ContactInfo() {
  const contactMethods: ContactMethod[] = [
    {
      icon: <Mail className="w-6 h-6" />,
      label: 'Email',
      value: '<EMAIL>',
      href: 'mailto:<EMAIL>',
      description: 'Best way to reach me for professional inquiries'
    },
    {
      icon: <Phone className="w-6 h-6" />,
      label: 'Phone',
      value: '+64 21 XXX XXXX',
      href: 'tel:+6421XXXXXXX',
      description: 'Available for calls during NZ business hours'
    },
    {
      icon: <MapPin className="w-6 h-6" />,
      label: 'Location',
      value: 'Christchurch, New Zealand',
      href: 'https://maps.google.com/?q=Christchurch,New+Zealand',
      description: 'Open to opportunities throughout New Zealand'
    }
  ];

  const socialLinks: SocialLink[] = [
    {
      name: 'GitHub',
      icon: <Github className="w-5 h-5" />,
      href: 'https://github.com/yourusername',
      username: '@yourusername',
      description: 'View my code and open source contributions'
    },
    {
      name: 'LinkedIn',
      icon: <Linkedin className="w-5 h-5" />,
      href: 'https://linkedin.com/in/yourprofile',
      username: '/in/yourprofile',
      description: 'Connect with me professionally'
    },
    {
      name: 'Twitter',
      icon: <Twitter className="w-5 h-5" />,
      href: 'https://twitter.com/yourusername',
      username: '@yourusername',
      description: 'Follow my tech journey and insights'
    }
  ];

  const handleResumeDownload = () => {
    // In a real implementation, this would download the actual resume file
    // For now, we'll just show an alert
    alert('Resume download would start here. Please add your actual resume file to the public folder.');
    
    // Example of actual download implementation:
    // const link = document.createElement('a');
    // link.href = '/resume.pdf';
    // link.download = 'YourName_Resume.pdf';
    // link.click();
  };

  return (
    <div className="space-y-8">
      {/* Contact Methods */}
      <AnimatedSection>
        <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg p-8">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            Get In Touch
          </h3>
          
          <div className="space-y-6">
            {contactMethods.map((method, index) => (
              <AnimatedSection key={method.label} delay={index * 100}>
                <a
                  href={method.href}
                  target={method.href.startsWith('http') ? '_blank' : undefined}
                  rel={method.href.startsWith('http') ? 'noopener noreferrer' : undefined}
                  className="flex items-start space-x-4 p-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors group"
                >
                  <div className="flex-shrink-0 w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center text-blue-600 dark:text-blue-400 group-hover:bg-blue-200 dark:group-hover:bg-blue-900/50 transition-colors">
                    {method.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                        {method.label}
                      </h4>
                      {method.href.startsWith('http') && (
                        <ExternalLink className="w-4 h-4 text-gray-400" />
                      )}
                    </div>
                    <p className="text-blue-600 dark:text-blue-400 font-medium">
                      {method.value}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                      {method.description}
                    </p>
                  </div>
                </a>
              </AnimatedSection>
            ))}
          </div>
        </div>
      </AnimatedSection>

      {/* Resume Download */}
      <AnimatedSection delay={300}>
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-8 border border-blue-100 dark:border-blue-800">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <Download className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
              Download My Resume
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Get a comprehensive overview of my skills, experience, and achievements in PDF format.
            </p>
            <button
              onClick={handleResumeDownload}
              className="inline-flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              <Download className="w-5 h-5" />
              <span>Download Resume (PDF)</span>
            </button>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-3">
              Last updated: December 2024 • 2 pages
            </p>
          </div>
        </div>
      </AnimatedSection>

      {/* Social Links */}
      <AnimatedSection delay={400}>
        <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg p-8">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            Connect With Me
          </h3>
          
          <div className="grid grid-cols-1 gap-4">
            {socialLinks.map((social, index) => (
              <AnimatedSection key={social.name} delay={500 + index * 100}>
                <a
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center space-x-4 p-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors group"
                >
                  <div className="flex-shrink-0 w-10 h-10 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center text-gray-600 dark:text-gray-400 group-hover:bg-blue-100 dark:group-hover:bg-blue-900/30 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                    {social.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <h4 className="font-semibold text-gray-900 dark:text-white">
                        {social.name}
                      </h4>
                      <ExternalLink className="w-4 h-4 text-gray-400" />
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {social.username}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                      {social.description}
                    </p>
                  </div>
                </a>
              </AnimatedSection>
            ))}
          </div>
        </div>
      </AnimatedSection>

      {/* Quick Stats */}
      <AnimatedSection delay={600}>
        <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg p-8">
          <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6 text-center">
            Let's Work Together
          </h3>
          
          <div className="grid grid-cols-2 gap-6 text-center">
            <div>
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-1">
                24h
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Typical response time
              </p>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600 dark:text-green-400 mb-1">
                100%
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Professional commitment
              </p>
            </div>
          </div>

          <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <p className="text-sm text-gray-500 dark:text-gray-400 text-center">
              Available for full-time opportunities, freelance projects, and collaborations
            </p>
          </div>
        </div>
      </AnimatedSection>
    </div>
  );
}
